#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TP-Link网站结构分析工具
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import json

def analyze_tplink_page(url):
    """分析TP-Link产品页面结构"""
    
    # 初始化WebDriver
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        print(f"正在访问页面: {url}")
        driver.get(url)
        time.sleep(5)
        
        # 滚动到页面底部
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        # 获取页面内容
        content = driver.page_source
        soup = BeautifulSoup(content, 'lxml')
        
        print("\n=== TP-Link页面结构分析 ===")
        
        # 1. 分析产品基本信息
        print("\n1. 产品基本信息分析:")
        
        # 产品名称
        title_selectors = [
            'h1', 'h2', 'h3', 'h4', '.product-title', '.product-name', 
            '[class*="title"]', '[class*="name"]'
        ]
        
        for selector in title_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if text and len(text) > 5 and len(text) < 100:
                    print(f"  可能的产品名称 ({selector}): {text}")
        
        # 价格信息
        print("\n2. 价格信息分析:")
        
        # 查找所有包含价格符号的元素
        all_elements = soup.find_all(string=lambda text: text and ('¥' in text or '￥' in text))
        for element in all_elements:
            parent = element.parent
            if parent:
                text = parent.get_text(strip=True)
                if text and len(text) < 50:
                    print(f"  可能的价格: {text} - 标签: {parent.name} - 类: {parent.get('class', [])}")
        
        # 3. 分析产品图片
        print("\n3. 产品图片分析:")
        images = soup.find_all('img')
        product_images = []
        
        for img in images:
            src = img.get('src') or img.get('data-src')
            if src:
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = 'https://www.tp-linkshop.com.cn' + src
                
                # 过滤产品相关图片
                if any(keyword in src.lower() for keyword in ['product', 'images', 'content', '1329']):
                    product_images.append(src)
                    print(f"  产品图片: {src}")
        
        # 4. 分析技术参数区域
        print("\n4. 技术参数分析:")
        
        # 查找规格参数标签
        spec_keywords = ['规格参数', '技术参数', '参数', '规格', 'specifications', 'specs']
        spec_tabs = []
        
        for keyword in spec_keywords:
            elements = soup.find_all(string=lambda text: text and keyword in text)
            for element in elements:
                parent = element.parent
                if parent:
                    spec_tabs.append(parent)
                    print(f"  找到参数标签: {element.strip()} - {parent.name} - {parent.get('class', [])}")
        
        # 查找参数表格
        tables = soup.find_all('table')
        print(f"  找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            if len(rows) > 1:  # 有意义的表格
                print(f"    表格 {i+1}: {len(rows)} 行")
                for j, row in enumerate(rows[:3]):  # 只显示前3行
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 1:
                        cell_texts = [cell.get_text(strip=True) for cell in cells]
                        print(f"      行 {j+1}: {' | '.join(cell_texts)}")
        
        # 5. 分析页面标签结构
        print("\n5. 页面标签分析:")
        
        # 查找所有可能的标签
        tab_elements = soup.find_all(['a', 'div', 'span', 'li'])
        
        for tab in tab_elements:
            text = tab.get_text(strip=True)
            if text and len(text) < 20 and any(keyword in text for keyword in ['商品', '规格', '评价', '咨询']):
                print(f"  标签: {text} - {tab.name} - {tab.get('class', [])} - {tab.get('id', '')}")
        
        # 6. 查找所有包含技术关键词的文本
        print("\n6. 技术关键词分析:")
        
        tech_keywords = [
            '频率', '速率', '天线', '功率', 'MHz', 'GHz', 'Mbps', 'Gbps', 'dBi', 'W', 'IEEE',
            '工作温度', '协议', '端口', '尺寸', '重量', '支持', '标准', 'Wi-Fi', 'WAN', 'LAN',
            '接口', '带宽', '传输', '覆盖', '范围', '电源', '功耗', '认证', '加密', '安全',
            '内置', '外置', 'PoE', 'RJ45', '以太网'
        ]
        
        page_text = soup.get_text()
        lines = page_text.split('\n')
        
        tech_lines = []
        for line in lines:
            line = line.strip()
            if line and any(keyword in line for keyword in tech_keywords):
                if len(line) < 200:
                    tech_lines.append(line)
        
        print(f"  找到 {len(tech_lines)} 行包含技术关键词的文本:")
        for line in tech_lines[:15]:  # 只显示前15行
            print(f"    {line}")
        
        # 7. 分析页面的特殊结构
        print("\n7. 页面特殊结构分析:")
        
        # 查找所有div的class属性
        divs_with_class = soup.find_all('div', class_=True)
        class_names = set()
        for div in divs_with_class:
            classes = div.get('class', [])
            for cls in classes:
                if any(keyword in cls.lower() for keyword in ['param', 'spec', 'detail', 'info', 'content']):
                    class_names.add(cls)
        
        print(f"  相关CSS类名: {list(class_names)}")
        
        # 8. 尝试点击规格参数标签
        print("\n8. 尝试交互操作:")
        
        try:
            # 查找规格参数标签并点击
            spec_tab = driver.find_element(By.XPATH, "//a[contains(text(), '规格参数')] | //div[contains(text(), '规格参数')] | //span[contains(text(), '规格参数')]")
            if spec_tab:
                driver.execute_script("arguments[0].click();", spec_tab)
                print("  成功点击规格参数标签")
                time.sleep(3)
                
                # 重新获取页面内容
                new_content = driver.page_source
                new_soup = BeautifulSoup(new_content, 'lxml')
                
                # 查找新出现的参数内容
                new_tables = new_soup.find_all('table')
                print(f"  点击后找到 {len(new_tables)} 个表格")
                
                for i, table in enumerate(new_tables):
                    rows = table.find_all('tr')
                    if len(rows) > 2:
                        print(f"    新表格 {i+1}: {len(rows)} 行")
                        for j, row in enumerate(rows[:5]):
                            cells = row.find_all(['td', 'th'])
                            if len(cells) >= 2:
                                cell_texts = [cell.get_text(strip=True) for cell in cells]
                                print(f"      行 {j+1}: {' | '.join(cell_texts)}")
        except Exception as e:
            print(f"  无法点击规格参数标签: {e}")
        
        print("\n=== 分析完成 ===")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    url = "https://www.tp-linkshop.com.cn/Products/Details/2453"
    analyze_tplink_page(url)
