#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一爬虫入口
支持锐捷和TP-Link产品爬取
"""

import sys
import os
from urllib.parse import urlparse
from crawler import RuijieCrawler
from tplink_crawler import TPLinkCrawler

def detect_website_type(url):
    """检测网站类型"""
    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()
    
    if 'ruijiery.com' in domain:
        return 'ruijie'
    elif 'tp-linkshop.com.cn' in domain:
        return 'tplink'
    else:
        return 'unknown'

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("统一爬虫 - 支持锐捷和TP-Link产品")
        print("用法: python unified_crawler.py <产品URL>")
        print("\n支持的网站:")
        print("  - 锐捷: https://www.ruijiery.com/product/...")
        print("  - TP-Link: https://www.tp-linkshop.com.cn/Products/Details/...")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # 检测网站类型
    website_type = detect_website_type(url)
    
    if website_type == 'unknown':
        print("错误: 不支持的网站")
        print("支持的网站:")
        print("  - 锐捷: https://www.ruijiery.com/product/...")
        print("  - TP-Link: https://www.tp-linkshop.com.cn/Products/Details/...")
        sys.exit(1)
    
    print(f"检测到网站类型: {website_type.upper()}")
    print(f"开始爬取产品: {url}")
    
    # 根据网站类型选择对应的爬虫
    if website_type == 'ruijie':
        crawler = RuijieCrawler(url)
        result = crawler.crawl()
        output_prefix = "reyee"
    elif website_type == 'tplink':
        crawler = TPLinkCrawler(url)
        result = crawler.crawl()
        output_prefix = "tplink"
    
    if result:
        print(f"✓ 爬取完成！")
        print(f"输出目录: {result}")
    else:
        print("✗ 爬取失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
