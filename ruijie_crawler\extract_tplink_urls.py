#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从MCP获取的TP-Link网页内容中提取产品链接
"""

import re
from urllib.parse import urljoin

def extract_product_links_from_html(html_content, base_url="https://www.tp-linkshop.com.cn"):
    """从HTML内容中提取产品链接"""
    # 使用正则表达式提取产品链接
    # 产品链接格式: /Products/Details/数字ID
    product_pattern = r'href="(/Products/Details/\d+)"'
    matches = re.findall(product_pattern, html_content)
    
    # 转换为完整URL并去重
    product_urls = []
    seen_urls = set()
    
    for match in matches:
        full_url = urljoin(base_url, match)
        if full_url not in seen_urls:
            seen_urls.add(full_url)
            product_urls.append(full_url)
    
    return product_urls

def main():
    """主函数"""
    
    # 从MCP获取的所有产品ID（从各个页面提取）
    product_ids = [
        # 新品页面 (289-463)
        "2740", "2739", "2449", "2453",
        
        # Wi-Fi 6无线路由器页面 (289-182)
        "2738", "2742", "2743", "2744", "2449", "2453", "2544", "2607", "2612", 
        "2051", "2053", "2745", "2746", "2747", "2748",
        
        # 易展路由器页面 (289-190)
        "2532", "1614", "1618", "1656", "1658", "2052", "2754", "2755", "2756", 
        "2757", "2758", "2759", "2449", "2453",
        
        # Wi-Fi 5无线路由器页面 (289-194)
        "2741", "2552", "1657", "1660",
        
        # Wi-Fi 4无线路由器页面 (289-198)
        "2311", "1642",
        
        # 扩展器/迷你路由/HyFi/电力线页面 (289-253)
        "1681",
        
        # 商用新品页面 (221-40)
        "2442",
        
        # 无线接入点（AP）页面 (221-52)
        "2653", "2698", "2704", "2706", "1628", "1635", "1884", "1972", "1974", 
        "1980", "1981", "1982", "1987", "1988", "1991", "2018", "2019", "2020", 
        "2021", "2056", "2063", "2065", "2066", "2067", "2179", "2442",
        
        # 无线控制器（AC）页面 (221-191)
        "1829", "1830", "2045", "2047",
        
        # 其他在页面中发现的产品ID
        "1594", "1595", "1582", "1583", "1580", "1549", "1562", "1588", "1592", 
        "1589", "1567", "1572", "1554"
    ]
    
    print("开始生成TP-Link产品链接...")
    
    # 去重产品ID
    unique_product_ids = list(set(product_ids))
    unique_product_ids.sort()  # 排序
    
    # 生成完整的产品URL
    base_url = "https://www.tp-linkshop.com.cn"
    product_urls = []
    
    for product_id in unique_product_ids:
        url = f"{base_url}/Products/Details/{product_id}"
        product_urls.append(url)
    
    print(f"总共生成 {len(product_urls)} 个产品链接")
    
    # 保存到文件
    output_file = "tplink_urls.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入文件头部注释
            f.write("# TP-Link产品URL列表\n")
            f.write("# 每行一个URL，以#开头的行为注释\n")
            f.write("# 从以下9个页面提取的产品链接：\n")
            f.write("# 1. 新品页面\n")
            f.write("# 2. Wi-Fi 6无线路由器页面\n")
            f.write("# 3. 易展路由器页面\n")
            f.write("# 4. Wi-Fi 5无线路由器页面\n")
            f.write("# 5. Wi-Fi 4无线路由器页面\n")
            f.write("# 6. 扩展器/迷你路由/HyFi/电力线页面\n")
            f.write("# 7. 商用新品页面\n")
            f.write("# 8. 无线接入点（AP）页面\n")
            f.write("# 9. 无线控制器（AC）页面\n")
            f.write("\n")
            
            for url in product_urls:
                f.write(url + '\n')
        
        print(f"产品链接已保存到: {output_file}")
        
        # 显示前10个链接作为示例
        print("\n前10个产品链接:")
        for i, url in enumerate(product_urls[:10], 1):
            print(f"{i}. {url}")
            
        if len(product_urls) > 10:
            print(f"... 还有 {len(product_urls) - 10} 个链接")
            
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
