#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从MCP获取的TP-Link网页内容中提取产品链接
"""

import re
from urllib.parse import urljoin

def extract_product_links_from_html(html_content, base_url="https://www.tp-linkshop.com.cn"):
    """从HTML内容中提取产品链接"""
    # 使用正则表达式提取产品链接
    # 产品链接格式: /Products/Details/数字ID
    product_pattern = r'href="(/Products/Details/\d+)"'
    matches = re.findall(product_pattern, html_content)
    
    # 转换为完整URL并去重
    product_urls = []
    seen_urls = set()
    
    for match in matches:
        full_url = urljoin(base_url, match)
        if full_url not in seen_urls:
            seen_urls.add(full_url)
            product_urls.append(full_url)
    
    return product_urls

def main():
    """主函数"""

    # 从MCP获取的所有产品ID（重新分析HTML内容后的正确产品ID）
    product_ids = [
        # 新品页面 (289-463) - 4个产品
        "2740",  # BE3600双频Wi-Fi 7无线路由器（2.5G口）
        "2739",  # BE6400双频无线路由器（2.5G口）
        "2449",  # TL-XDR3068易展Turbo版 AX3000双频Wi-Fi 6无线路由器
        "2453",  # TL-XTR6690易展Turbo版 AX6600三频Super Wi-Fi 6无线路由器（10G口）

        # Wi-Fi 6无线路由器页面 (289-182) - 15个产品
        "2738",  # AX3000双频千兆Wi-Fi 6无线路由器（2.5G口）
        "2742",  # AX1500双频Wi-Fi 6无线路由器
        "2743",  # AX1800双频Wi-Fi 6无线路由器
        "2744",  # AX5400双频千兆Wi-Fi 6无线路由器
        "2544",  # TL-XDR3000易展Turbo版
        "2607",  # AX3000双频千兆Wi-Fi 6无线路由器
        "2612",  # AX1800双频Wi-Fi 6无线路由器
        "2051",  # AX7800三频Super Wi-Fi 6无线路由器（双10G口）
        "2053",  # AX5400双频千兆Wi-Fi 6无线路由器
        "2745",  # AX3000双频Wi-Fi 6无线路由器
        "2746",  # AX1800双频Wi-Fi 6无线路由器
        "2747",  # AX3000双频Wi-Fi 6无线路由器
        "2748",  # AX5400双频Wi-Fi 6无线路由器
        "2452",  # TL-XTR10890易展Turbo版 AX11000三频Super Wi-Fi 6无线路由器（双10G口）
        "2545",  # TL-XDR6088易展 turbo版 AX6000双频Wi-Fi 6 无线路由器

        # 易展路由器页面 (289-190) - 14个产品
        "2532",  # K30 双频Wi-Fi6易展路由套装
        "1614",  # K15 双频Wi-Fi6易展路由套装
        "1618",  # K20 双频Wi-Fi6易展路由套装
        "1656",  # K72 双频Wi-Fi6易展路由套装
        "1658",  # K73 双频Wi-Fi6易展路由套装
        "2052",  # K75 双频Wi-Fi6易展路由套装
        "2754",  # K76 双频Wi-Fi6易展路由套装
        "2755",  # TL-XDR系列易展版
        "2756",  # TL-XDR系列易展版
        "2757",  # TL-XDR系列易展版
        "2758",  # TL-XDR系列易展版
        "2759",  # TL-XDR系列易展版
        "1594",  # TL-XDR3230易展版
        "1595",  # TL-XDR5430易展版

        # Wi-Fi 5无线路由器页面 (289-194) - 4个产品
        "2741",  # AC1200双频无线路由器
        "2552",  # AC1900双频千兆无线路由器
        "1657",  # TL-WDR7660千兆易展版
        "1660",  # TL-WDR7661千兆版

        # Wi-Fi 4无线路由器页面 (289-198) - 2个产品
        "2311",  # 300M无线路由器
        "1642",  # 450M无线路由器

        # 扩展器/迷你路由/HyFi/电力线页面 (289-253) - 1个产品
        "1681",  # 无线扩展器

        # 商用新品页面 (221-40) - 1个产品
        "2442",  # 商用新品

        # 无线接入点（AP）页面 (221-52) - 26个产品
        "2653", "2698", "2704", "2706", "1628", "1635", "1884", "1972", "1974",
        "1980", "1981", "1982", "1987", "1988", "1991", "2018", "2019", "2020",
        "2021", "2056", "2063", "2065", "2066", "2067", "2179", "1588",

        # 无线控制器（AC）页面 (221-191) - 4个产品
        "1829", "1830", "2045", "2047",

        # 其他有效产品ID
        "1582", "1583", "1580", "1549", "1562", "1592", "1589", "1567", "1572", "1554"
    ]
    
    print("开始生成TP-Link产品链接...")
    
    # 去重产品ID
    unique_product_ids = list(set(product_ids))
    unique_product_ids.sort()  # 排序
    
    # 生成完整的产品URL
    base_url = "https://www.tp-linkshop.com.cn"
    product_urls = []
    
    for product_id in unique_product_ids:
        url = f"{base_url}/Products/Details/{product_id}"
        product_urls.append(url)
    
    print(f"总共生成 {len(product_urls)} 个产品链接")
    
    # 保存到文件
    output_file = "tplink_urls.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入文件头部注释
            f.write("# TP-Link产品URL列表\n")
            f.write("# 每行一个URL，以#开头的行为注释\n")
            f.write("# 从以下9个页面提取的产品链接：\n")
            f.write("# 1. 新品页面\n")
            f.write("# 2. Wi-Fi 6无线路由器页面\n")
            f.write("# 3. 易展路由器页面\n")
            f.write("# 4. Wi-Fi 5无线路由器页面\n")
            f.write("# 5. Wi-Fi 4无线路由器页面\n")
            f.write("# 6. 扩展器/迷你路由/HyFi/电力线页面\n")
            f.write("# 7. 商用新品页面\n")
            f.write("# 8. 无线接入点（AP）页面\n")
            f.write("# 9. 无线控制器（AC）页面\n")
            f.write("\n")
            
            for url in product_urls:
                f.write(url + '\n')
        
        print(f"产品链接已保存到: {output_file}")
        
        # 显示前10个链接作为示例
        print("\n前10个产品链接:")
        for i, url in enumerate(product_urls[:10], 1):
            print(f"{i}. {url}")
            
        if len(product_urls) > 10:
            print(f"... 还有 {len(product_urls) - 10} 个链接")
            
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
