#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
锐捷产品爬虫主程序
支持单个URL和批量URL处理
"""

import sys
import os
from crawler import RuijieCrawler
from batch_crawler import BatchRuijieCrawler

def print_usage():
    """打印使用说明"""
    print("锐捷产品爬虫 - 使用说明")
    print("=" * 50)
    print("单个产品爬取:")
    print("  python main.py <产品页面URL>")
    print("  示例: python main.py https://www.ruijie.com.cn/cp/wlan-ap/rg-eap162mg/")
    print()
    print("批量产品爬取:")
    print("  python main.py --batch")
    print("  然后按提示输入多个URL")
    print()
    print("从文件批量爬取:")
    print("  python main.py --file <url文件>")
    print("  示例: python main.py --file urls_example.txt")
    print()
    print("功能特点:")
    print("  ✓ 完整页面长截图（优化拼接算法）")
    print("  ✓ 技术参数提取（参数名：参数值格式）")
    print("  ✓ 按 reyee/{产品名称} 目录保存")
    print("  ✓ 支持批量处理多个产品")

def single_crawl(product_url):
    """单个产品爬取"""
    print(f"开始爬取产品: {product_url}")
    
    # 创建爬虫实例
    crawler = RuijieCrawler(product_url)
    
    # 执行爬取
    success = crawler.crawl()
    
    if success:
        print("✓ 爬取完成！")
        if hasattr(crawler, 'output_dir'):
            print(f"输出目录: {crawler.output_dir}")
    else:
        print("✗ 爬取失败！")
    
    return success

def batch_crawl():
    """批量爬取（交互式输入）"""
    print("批量产品爬取模式")
    print("请输入产品URL，每行一个，输入空行结束:")
    
    urls = []
    while True:
        url = input().strip()
        if not url:
            break
        if url.startswith('http'):
            urls.append(url)
        else:
            print(f"无效的URL: {url}")
    
    if not urls:
        print("没有有效的URL")
        return False
    
    # 执行批量爬取
    batch_crawler = BatchRuijieCrawler()
    batch_crawler.crawl_urls(urls)
    batch_crawler.save_batch_report()
    batch_crawler.print_summary()
    
    return True

def file_crawl(url_file):
    """从文件批量爬取"""
    if not os.path.exists(url_file):
        print(f"错误: 文件不存在: {url_file}")
        return False
    
    # 读取URL
    urls = []
    try:
        with open(url_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                if line.startswith('http'):
                    urls.append(line)
                else:
                    print(f"警告: 第{line_num}行不是有效的URL: {line}")
    except Exception as e:
        print(f"错误: 读取文件失败: {e}")
        return False
    
    if not urls:
        print("文件中没有有效的URL")
        return False
    
    print(f"从文件 {url_file} 读取到 {len(urls)} 个URL")
    
    # 执行批量爬取
    batch_crawler = BatchRuijieCrawler()
    batch_crawler.crawl_urls(urls)
    batch_crawler.save_batch_report()
    batch_crawler.print_summary()
    
    return True

def main():
    if len(sys.argv) == 1:
        print_usage()
        return
    
    if sys.argv[1] == "--batch":
        batch_crawl()
    elif sys.argv[1] == "--file":
        if len(sys.argv) != 3:
            print("错误: 请指定URL文件")
            print("用法: python main.py --file <url文件>")
            return
        file_crawl(sys.argv[2])
    elif sys.argv[1].startswith('http'):
        single_crawl(sys.argv[1])
    else:
        print("错误: 无效的参数")
        print_usage()

if __name__ == "__main__":
    main()
