# 锐捷产品爬虫工具

一个专门用于爬取锐捷网络产品信息的Python工具，支持单个产品和批量产品爬取，具备完整页面长截图和技术参数提取功能。

## 🚀 功能特点

- ✅ **完整页面长截图**: 优化拼接算法，避免重叠错误，生成高质量长截图
- ✅ **技术参数提取**: 参数名：参数值格式，结构化保存技术规格信息
- ✅ **智能目录管理**: 按 `reyee/{产品名称}` 方式自动创建和管理输出目录
- ✅ **批量处理支持**: 支持多个产品URL批量爬取，提高工作效率
- ✅ **自动化浏览器**: 无头模式运行，自动处理页面交互和动态内容
- ✅ **详细日志记录**: 完整的爬取过程记录和错误处理

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🎯 使用方法

### 1. 单个产品爬取

```bash
python main.py <产品页面URL>
```

示例：
```bash
python main.py https://www.ruijie.com.cn/cp/wlan-ap/rg-eap162mg/
```

### 2. 批量产品爬取（交互式）

```bash
python main.py --batch
```

然后按提示输入多个URL，每行一个，输入空行结束。

### 3. 从文件批量爬取

```bash
python main.py --file urls_example.txt
```

URL文件格式：
```
# 锐捷产品URL列表
https://www.ruijie.com.cn/cp/wlan-ap/rg-eap162mg/
https://www.ruijie.com.cn/cp/wlan-ap/rg-eap110/
# 以#开头的行为注释
```

### 4. 其他批量爬取方式

直接使用批量爬虫：
```bash
python batch_crawler.py <URL1> <URL2> <URL3>
```

从文件读取：
```bash
python batch_crawler_from_file.py urls_example.txt
```

## 📁 输出文件结构

爬取结果按产品名称分别保存在 `reyee/{产品名称}/` 目录下：

```
reyee/
├── RG-EAP162(MG)/
│   ├── images/
│   │   └── full_page_screenshot.png
│   └── texts/
│       └── tech_specifications.txt
├── 其他产品名称/
│   ├── images/
│   └── texts/
└── batch_report.txt  # 批量处理报告
```

### 技术参数格式示例

```
技术参数
==================================================
提取时间: 2024-01-20 15:30:45
页面URL: https://www.ruijie.com.cn/cp/wlan-ap/rg-eap162mg/
==================================================

是否支持11AX 协议(Wi-Fi 6)：支持
业务端口：1个上联10/100/1000/2500 Mbps自适应以太网接口， 1个下联10/100/1000/2500 Mbps自适应以太网接口
工作频率：2.4GHz、5GHz
最大发射功率：23dBm
天线增益：2.4GHz: 2dBi, 5GHz: 2dBi
```

## 📋 提取的信息

- **产品名称**: 自动识别页面标题
- **产品价格**: 提取￥符号后的价格信息
- **产品特征**: 包含Wi-Fi、网络、天线等关键词的特征描述
- **完整页面截图**: 整个页面的长截图，包含所有内容
- **技术参数**: 详细的技术规格和参数信息

## 🛠️ 技术架构

- **单文件爬虫** (`crawler.py`): 包含所有核心功能的简化爬虫
- **完整页面截图**: 分段截图并自动拼接成长图
- **智能参数提取**: 自动点击技术参数标签并提取信息
- **错误处理**: 完善的异常处理和日志记录

## 🔍 使用示例

### 爬取产品信息

```bash
python crawler.py
# 输入: https://www.ruijiery.com/product/1796066620307464193.html?key=1
```

**输出**:
```
产品名称: AX3000双频Wi-Fi 6室内2.5G墙面AP RG-EAP162(MG) 一年质保
产品价格: ￥700
产品特征:
• Wi-Fi 6室内2.5G面板AP
• AX3000双频技术
• 适用于酒店、商业地产等场景
```

**生成文件**:
- `full_page_screenshot.png` - 完整页面长截图
- `product_info.txt` - 产品基本信息
- `tech_specifications.txt` - 技术参数详细信息

## ⚠️ 注意事项

1. **网络连接**: 确保网络连接稳定
2. **Chrome浏览器**: 需要安装Chrome浏览器（自动下载ChromeDriver）
3. **内存使用**: 长截图可能占用较多内存，建议在内存充足的环境下运行
4. **URL格式**: 确保输入的是有效的锐捷产品页面URL

## 🐛 故障排除

### 常见问题

1. **WebDriver初始化失败**
   - 检查Chrome浏览器是否已安装
   - 检查网络连接是否正常

2. **截图失败**
   - 检查内存是否充足
   - 页面可能过长，会自动分段处理

3. **技术参数提取失败**
   - 页面结构可能发生变化
   - 检查是否有技术参数标签

### 日志查看

程序运行时会在控制台显示详细的日志信息，包括：
- 页面访问状态
- 截图进度
- 技术参数提取结果
- 错误信息

## 📞 技术支持

如果遇到问题，请检查：
1. 依赖包是否正确安装（特别是PIL/Pillow）
2. 网络连接是否正常
3. 目标URL是否有效
4. Chrome浏览器是否可用

## 🔄 更新日志

- **v3.0**: 简化工程结构，实现完整页面长截图
- **v2.0**: 支持动态URL，移除硬编码图片链接
- **v1.0**: 基础版本，支持固定URL爬取
