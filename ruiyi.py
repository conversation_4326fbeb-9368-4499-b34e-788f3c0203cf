import os
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup

# 配置Selenium
chrome_options = Options()
chrome_options.add_argument('--headless')
chrome_options.add_argument('--disable-gpu')
chrome_options.add_argument('--no-sandbox')
driver = webdriver.Chrome(options=chrome_options)

url = 'https://www.ruijiery.com/product/1925441870023266305.html'
driver.get(url)
time.sleep(2)  # 等待页面加载

# 创建保存目录
def ensure_dir(path):
    if not os.path.exists(path):
        os.makedirs(path)

save_dir = 'output'
ensure_dir(save_dir)

# 获取主页信息
soup = BeautifulSoup(driver.page_source, 'html.parser')

# 1. 产品缩略图
thumb_img = soup.select_one('div.product-img img')
if thumb_img and thumb_img.get('src'):
    img_url = thumb_img['src']
    if not img_url.startswith('http'):
        img_url = 'https://www.ruijiery.com' + img_url
    img_data = requests.get(img_url).content
    with open(os.path.join(save_dir, 'thumb.jpg'), 'wb') as f:
        f.write(img_data)

# 2. 价格、库存、销量、评价等
info_texts = []
price_info = soup.select_one('div.product-info')
if price_info:
    info_texts.append(price_info.get_text(strip=True, separator='\n'))

# 3. 产品特征
feature = soup.find('div', class_='product-feature')
if feature:
    info_texts.append('产品特征:')
    info_texts.append(feature.get_text(strip=True, separator='\n'))

# 保存主页文本信息
txt_path = os.path.join(save_dir, 'main_info.txt')
with open(txt_path, 'w', encoding='utf-8') as f:
    f.write('\n'.join(info_texts))

# 4. 点击“商品详情”标签，获取文本和图片
def click_and_save(tab_text, txt_filename, img_prefix):
    try:
        # 定位tab为a标签
        tab = driver.find_element(By.XPATH, f"//a[normalize-space(text())='{tab_text}']")
        driver.execute_script("arguments[0].scrollIntoView();", tab)
        tab.click()
        time.sleep(1)
        # 解析新内容
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        # tab内容区：tab导航后紧跟的内容区
        # 先找到当前tab的a标签，再找其父级，内容区一般紧跟其后
        nav = soup.find('a', string=tab_text)
        texts = []
        images = []
        if nav:
            # 找到tab导航的父级
            nav_parent = nav.find_parent()
            # 找到内容区（下一个兄弟元素）
            content_div = nav_parent.find_next_sibling()
            if content_div:
                texts.append(content_div.get_text(strip=True, separator='\n'))
                images = content_div.find_all('img')
        # 保存文本
        with open(os.path.join(save_dir, txt_filename), 'w', encoding='utf-8') as f:
            f.write('\n'.join(texts))
        # 保存图片
        for idx, img in enumerate(images):
            img_url = img.get('src')
            if img_url and not img_url.startswith('data:'):
                if not img_url.startswith('http'):
                    img_url = 'https://www.ruijiery.com' + img_url
                img_data = requests.get(img_url).content
                with open(os.path.join(save_dir, f'{img_prefix}_{idx+1}.jpg'), 'wb') as f_img:
                    f_img.write(img_data)
    except Exception as e:
        print(f"[ERROR] Tab '{tab_text}' 未找到或内容抓取失败: {e}")

# 商品详情
click_and_save('商品详情', 'detail.txt', 'detail_img')
# 技术参数
click_and_save('技术参数', 'spec.txt', 'spec_img')

driver.quit()
print('抓取完成，数据已保存到output目录。')
