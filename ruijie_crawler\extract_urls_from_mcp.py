#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从MCP获取的HTML内容中提取产品链接
"""

import re
from urllib.parse import urljoin

def extract_product_links_from_html(html_content, base_url="https://www.ruijiery.com"):
    """从HTML内容中提取产品链接"""
    # 使用正则表达式提取产品链接
    # 产品链接格式: /product/数字ID.html
    product_pattern = r'href="(/product/\d+\.html)"'
    matches = re.findall(product_pattern, html_content)
    
    # 转换为完整URL并去重
    product_urls = []
    seen_urls = set()
    
    for match in matches:
        full_url = urljoin(base_url, match)
        if full_url not in seen_urls:
            seen_urls.add(full_url)
            product_urls.append(full_url)
    
    return product_urls

def main():
    """主函数"""

    # 直接定义所有产品ID
    product_ids = [
        # 面板AP产品
        "1833342812780605442",  # RG-EAP172(MG)
        "1796066620307464193",  # RG-EAP162(MG)
        "441329261898113280",   # RG-EAP102(E) 薄款
        "440853638940729600",   # RG-EAP162(G) V2
        "440400865322606848",   # RG-EAP162(E)
        "439351601928610048",   # RG-RAP1261(E)
        "435776993767989504",   # RG-EAP101 V2
        "435687175398096896",   # RG-EAP101
        "432040545749303296",   # RG-RAP1200(FE)
        "431975364062347264",   # RG-EAP102(F)
        "431973189834768384",   # RG-EAP102 V2
        "431833979023785984",   # RG-RAP1260(G)(Gray)
        "431833941086044160",   # RG-RAP1260(G)(Gold)
        "431832862071717888",   # RG-RAP1260(G)(White)
        "431832787674202112",   # RG-EAP162(G)
        "431832631550148608",   # RG-RAP1200(F)
        "431832579094872064",   # RG-RAP1200(E)

        # 家用产品
        "1925441870023266305",  # RG-EW5100BE PRO
        "1890240659805618178",  # RG-EW1300G
        "1815940967817990146",  # RG-EW6000GX PRO
        "1754741592510873601",  # RG-EW3000GX
        "435206683351326976",   # RG-EW3200GX
        "434278266349813760",   # RG-EW1800GX
        "432001294964621312",   # RG-EW1800GX PRO
        "432001235467894784",   # RG-EW1200G PRO
        "432001164210339840",   # RG-EW1200
    ]

    print("开始生成产品链接...")

    # 生成完整的产品URL
    base_url = "https://www.ruijiery.com"
    product_urls = []

    for product_id in product_ids:
        url = f"{base_url}/product/{product_id}.html"
        product_urls.append(url)

    print(f"总共生成 {len(product_urls)} 个产品链接")

    # 保存到文件
    output_file = "urls_example.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for url in product_urls:
                f.write(url + '\n')

        print(f"产品链接已保存到: {output_file}")

        # 显示所有链接
        print("\n所有产品链接:")
        for i, url in enumerate(product_urls, 1):
            print(f"{i}. {url}")

    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
