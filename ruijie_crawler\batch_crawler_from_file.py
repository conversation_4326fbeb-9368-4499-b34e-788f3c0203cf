#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从文件读取URL的批量爬虫
"""

import os
import sys
from batch_crawler import BatchRuijieCrawler

def read_urls_from_file(file_path):
    """从文件读取URL列表"""
    urls = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue
                
                # 验证URL格式
                if line.startswith('http'):
                    urls.append(line)
                else:
                    print(f"警告: 第{line_num}行不是有效的URL: {line}")
    
    except FileNotFoundError:
        print(f"错误: 文件不存在: {file_path}")
        return []
    except Exception as e:
        print(f"错误: 读取文件失败: {e}")
        return []
    
    return urls

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python batch_crawler_from_file.py <url_file>")
        print("示例: python batch_crawler_from_file.py urls_example.txt")
        return
    
    url_file = sys.argv[1]
    
    # 读取URL列表
    urls = read_urls_from_file(url_file)
    
    if not urls:
        print("没有找到有效的URL")
        return
    
    print(f"从文件 {url_file} 读取到 {len(urls)} 个URL:")
    for i, url in enumerate(urls, 1):
        print(f"{i}. {url}")
    
    confirm = input(f"\n确认开始批量爬取这 {len(urls)} 个产品? (y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消")
        return
    
    # 开始批量爬取
    batch_crawler = BatchRuijieCrawler()
    batch_crawler.crawl_urls(urls)
    
    # 保存报告和打印摘要
    batch_crawler.save_batch_report()
    batch_crawler.print_summary()

if __name__ == "__main__":
    main()
