#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取锐捷睿易网站产品链接的脚本
使用MCP获取网页内容并提取产品链接
"""

import re
import requests
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def extract_product_links_from_html(html_content, base_url="https://www.ruijiery.com"):
    """从HTML内容中提取产品链接"""
    # 使用正则表达式提取产品链接
    # 产品链接格式: /product/数字ID.html
    product_pattern = r'href="(/product/\d+\.html)"'
    matches = re.findall(product_pattern, html_content)
    
    # 转换为完整URL并去重
    product_urls = []
    seen_urls = set()
    
    for match in matches:
        full_url = urljoin(base_url, match)
        if full_url not in seen_urls:
            seen_urls.add(full_url)
            product_urls.append(full_url)
    
    return product_urls

def get_page_content(url):
    """获取网页内容"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        return response.text
    except Exception as e:
        print(f"获取页面 {url} 失败: {e}")
        return None

def main():
    """主函数"""
    # 定义要爬取的页面URL
    urls_to_crawl = [
        # 面板AP页面 (3页)
        "https://www.ruijiery.com/productList/wireless/panelap/1.html",
        "https://www.ruijiery.com/productList/wireless/panelap/2.html", 
        "https://www.ruijiery.com/productList/wireless/panelap/3.html",
        # 家用产品页面 (2页)
        "https://www.ruijiery.com/productList/home/<USER>",
        "https://www.ruijiery.com/productList/home/<USER>"
    ]
    
    all_product_urls = []
    
    print("开始提取产品链接...")
    
    for url in urls_to_crawl:
        print(f"正在处理: {url}")
        html_content = get_page_content(url)
        
        if html_content:
            product_urls = extract_product_links_from_html(html_content)
            all_product_urls.extend(product_urls)
            print(f"从 {url} 提取到 {len(product_urls)} 个产品链接")
        else:
            print(f"无法获取页面内容: {url}")
    
    # 去重
    unique_urls = list(set(all_product_urls))
    unique_urls.sort()  # 排序
    
    print(f"\n总共提取到 {len(unique_urls)} 个唯一产品链接")
    
    # 保存到文件
    output_file = "urls_example.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for url in unique_urls:
                f.write(url + '\n')
        
        print(f"产品链接已保存到: {output_file}")
        
        # 显示前几个链接作为示例
        print("\n前10个产品链接:")
        for i, url in enumerate(unique_urls[:10], 1):
            print(f"{i}. {url}")
            
        if len(unique_urls) > 10:
            print(f"... 还有 {len(unique_urls) - 10} 个链接")
            
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    main()
