#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门调试特定页面的技术参数提取
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

def debug_specific_page(url):
    """调试特定页面的技术参数提取"""
    
    # 初始化WebDriver
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        print(f"正在访问页面: {url}")
        driver.get(url)
        time.sleep(5)
        
        # 1. 滚动到页面底部
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        # 2. 点击技术参数标签
        print("\n=== 查找并点击技术参数标签 ===")
        tech_selectors = [
            "//a[contains(text(), '技术参数')]",
            "//div[contains(text(), '技术参数')]",
            "//span[contains(text(), '技术参数')]"
        ]
        
        clicked = False
        for selector in tech_selectors:
            try:
                element = driver.find_element(By.XPATH, selector)
                driver.execute_script("arguments[0].click();", element)
                print(f"成功点击: {selector}")
                time.sleep(5)
                clicked = True
                break
            except:
                continue
        
        if not clicked:
            print("未找到技术参数标签")
        
        # 3. 获取页面内容
        content = driver.page_source
        soup = BeautifulSoup(content, 'lxml')
        
        # 4. 分析页面结构
        print("\n=== 分析页面结构 ===")
        
        # 查找所有表格
        tables = soup.find_all('table')
        print(f"找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            print(f"\n--- 表格 {i+1} ---")
            rows = table.find_all('tr')
            print(f"行数: {len(rows)}")
            
            for j, row in enumerate(rows[:10]):  # 只显示前10行
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    print(f"  行 {j+1}: {' | '.join(cell_texts)}")
        
        # 5. 查找包含技术参数的div
        print("\n=== 查找技术参数div ===")
        
        # 查找所有包含技术关键词的div
        tech_keywords = ['WAN', 'LAN', '口数', '带宽', '功能', '温度', '湿度', '协议', '支持', '接口', 'Mbps', 'MHz', 'GHz']
        
        all_divs = soup.find_all('div')
        param_divs = []
        
        for div in all_divs:
            div_text = div.get_text(strip=True)
            if div_text and any(keyword in div_text for keyword in tech_keywords):
                if len(div_text) < 100:  # 过滤掉太长的文本
                    param_divs.append(div_text)
        
        print(f"找到 {len(param_divs)} 个可能的参数div:")
        for i, text in enumerate(param_divs[:20]):  # 只显示前20个
            print(f"  {i+1:2d}. {text}")
        
        # 6. 查找特定的参数容器
        print("\n=== 查找参数容器 ===")
        
        # 查找可能的参数容器
        containers = soup.find_all(['div', 'section'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['param', 'spec', 'tech', 'detail', 'info', 'grid', 'row', 'col']
        ))
        
        print(f"找到 {len(containers)} 个参数容器")
        for i, container in enumerate(containers):
            container_text = container.get_text(strip=True)
            if container_text:
                print(f"容器 {i+1}: {container_text[:100]}...")
        
        # 7. 尝试提取参数对
        print("\n=== 尝试提取参数对 ===")
        
        specifications = []
        
        # 方法1: 查找相邻的div配对
        for i, div in enumerate(all_divs):
            div_text = div.get_text(strip=True)
            
            # 如果这个div看起来像参数名
            if (div_text and len(div_text) < 50 and 
                any(keyword in div_text for keyword in tech_keywords)):
                
                # 查找相邻的div作为参数值
                for j in range(i+1, min(i+5, len(all_divs))):
                    next_div = all_divs[j]
                    next_text = next_div.get_text(strip=True)
                    
                    if (next_text and len(next_text) < 200 and next_text != div_text and
                        any(keyword in next_text for keyword in ['个', 'Mbps', '支持', '不支持', '℃', '%', 'mm', 'IEEE', 'MHz', 'GHz'])):
                        
                        specifications.append(f"{div_text}：{next_text}")
                        break
        
        # 方法2: 查找包含冒号的文本
        page_text = soup.get_text()
        lines = page_text.split('\n')
        
        for line in lines:
            line = line.strip()
            if (line and ('：' in line or ':' in line) and 
                any(keyword in line for keyword in tech_keywords)):
                
                if '：' in line:
                    parts = line.split('：', 1)
                else:
                    parts = line.split(':', 1)
                
                if len(parts) == 2:
                    param_name = parts[0].strip()
                    param_value = parts[1].strip()
                    if param_name and param_value and len(param_name) < 80:
                        specifications.append(f"{param_name}：{param_value}")
        
        # 去重
        unique_specs = []
        seen = set()
        for spec in specifications:
            if spec not in seen:
                unique_specs.append(spec)
                seen.add(spec)
        
        print(f"\n最终提取到 {len(unique_specs)} 个技术参数:")
        for i, spec in enumerate(unique_specs, 1):
            print(f"{i:2d}. {spec}")
        
        print("\n=== 调试完成 ===")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    url = "https://www.ruijiery.com/product/434278266349813760.html"
    debug_specific_page(url)
