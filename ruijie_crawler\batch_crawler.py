#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
锐捷产品批量爬虫
支持批量处理多个产品URL
"""

import os
import sys
import time
import logging
from typing import List, Dict
from crawler import RuijieCrawler

class BatchRuijieCrawler:
    """批量锐捷产品爬虫"""
    
    def __init__(self, output_base_dir="reyee"):
        self.output_base_dir = output_base_dir
        self.results = []
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO, 
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('batch_crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def crawl_urls(self, urls: List[str]) -> List[Dict]:
        """批量爬取多个URL"""
        self.logger.info(f"开始批量爬取 {len(urls)} 个产品页面")
        
        for i, url in enumerate(urls, 1):
            self.logger.info(f"正在处理第 {i}/{len(urls)} 个产品: {url}")
            
            try:
                # 创建单个爬虫实例
                crawler = RuijieCrawler(url, self.output_base_dir)
                
                # 执行爬取
                success = crawler.crawl()
                
                result = {
                    'url': url,
                    'success': success,
                    'product_name': getattr(crawler, 'product_name', 'Unknown'),
                    'output_dir': getattr(crawler, 'output_dir', None),
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                self.results.append(result)
                
                if success:
                    self.logger.info(f"✓ 产品 {result['product_name']} 爬取成功")
                else:
                    self.logger.error(f"✗ 产品爬取失败: {url}")
                
                # 清理资源
                if hasattr(crawler, 'driver') and crawler.driver:
                    crawler.driver.quit()
                
                # 间隔时间，避免请求过于频繁
                if i < len(urls):
                    self.logger.info("等待 3 秒后处理下一个产品...")
                    time.sleep(3)
                    
            except Exception as e:
                self.logger.error(f"处理URL失败 {url}: {e}")
                self.results.append({
                    'url': url,
                    'success': False,
                    'error': str(e),
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                })
        
        return self.results
    
    def save_batch_report(self):
        """保存批量处理报告"""
        try:
            report_file = os.path.join(self.output_base_dir, 'batch_report.txt')
            os.makedirs(self.output_base_dir, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("锐捷产品批量爬取报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总计处理: {len(self.results)} 个产品\n")
                
                success_count = sum(1 for r in self.results if r['success'])
                f.write(f"成功: {success_count} 个\n")
                f.write(f"失败: {len(self.results) - success_count} 个\n")
                f.write("=" * 50 + "\n\n")
                
                for i, result in enumerate(self.results, 1):
                    f.write(f"{i}. {result['url']}\n")
                    f.write(f"   状态: {'成功' if result['success'] else '失败'}\n")
                    if 'product_name' in result:
                        f.write(f"   产品名称: {result['product_name']}\n")
                    if 'output_dir' in result and result['output_dir']:
                        f.write(f"   输出目录: {result['output_dir']}\n")
                    if 'error' in result:
                        f.write(f"   错误: {result['error']}\n")
                    f.write(f"   时间: {result['timestamp']}\n\n")
            
            self.logger.info(f"批量处理报告已保存: {report_file}")
            
        except Exception as e:
            self.logger.error(f"保存批量报告失败: {e}")
    
    def print_summary(self):
        """打印处理摘要"""
        success_count = sum(1 for r in self.results if r['success'])
        total_count = len(self.results)
        
        print("\n" + "=" * 60)
        print("批量爬取完成摘要")
        print("=" * 60)
        print(f"总计处理: {total_count} 个产品")
        print(f"成功: {success_count} 个")
        print(f"失败: {total_count - success_count} 个")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        if success_count > 0:
            print("\n成功的产品:")
            for result in self.results:
                if result['success']:
                    print(f"  ✓ {result.get('product_name', 'Unknown')} - {result['output_dir']}")
        
        if total_count - success_count > 0:
            print("\n失败的产品:")
            for result in self.results:
                if not result['success']:
                    print(f"  ✗ {result['url']}")
                    if 'error' in result:
                        print(f"    错误: {result['error']}")
        
        print("=" * 60)

def main():
    """主函数 - 支持命令行参数或交互式输入"""
    if len(sys.argv) > 1:
        # 从命令行参数获取URL
        urls = sys.argv[1:]
    else:
        # 交互式输入
        print("锐捷产品批量爬虫")
        print("请输入产品URL，每行一个，输入空行结束:")
        
        urls = []
        while True:
            url = input().strip()
            if not url:
                break
            if url.startswith('http'):
                urls.append(url)
            else:
                print(f"无效的URL: {url}")
    
    if not urls:
        print("没有有效的URL，退出程序")
        return
    
    print(f"\n将要处理 {len(urls)} 个产品URL:")
    for i, url in enumerate(urls, 1):
        print(f"{i}. {url}")
    
    confirm = input("\n确认开始批量爬取? (y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消")
        return
    
    # 开始批量爬取
    batch_crawler = BatchRuijieCrawler()
    batch_crawler.crawl_urls(urls)
    
    # 保存报告和打印摘要
    batch_crawler.save_batch_report()
    batch_crawler.print_summary()

if __name__ == "__main__":
    main()
