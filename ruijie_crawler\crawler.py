#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
锐捷产品爬虫 - 简化版
功能：获取产品信息、完整页面长截图、技术参数
"""

import os
import time
import logging
import requests
import io
from urllib.parse import urljoin
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from PIL import Image
from urllib.parse import urlparse


class RuijieCrawler:
    """锐捷产品爬虫"""

    def __init__(self, product_url, output_base_dir="reyee"):
        self.product_url = product_url
        self.output_base_dir = output_base_dir
        self.driver = None
        self.product_name = None
        self.output_dir = None

        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

    def setup_output_directory(self, product_name):
        """根据产品名称设置输出目录"""
        try:
            # 清理产品名称，移除不合法的文件名字符
            safe_name = "".join(c for c in product_name if c.isalnum() or c in (' ', '-', '_', '(', ')')).strip()
            safe_name = safe_name.replace(' ', '_')

            # 创建 reyee/{产品名称} 目录结构
            self.output_dir = os.path.join(self.output_base_dir, safe_name)
            os.makedirs(self.output_dir, exist_ok=True)
            os.makedirs(os.path.join(self.output_dir, 'images'), exist_ok=True)
            os.makedirs(os.path.join(self.output_dir, 'texts'), exist_ok=True)

            self.logger.info(f"输出目录设置为: {self.output_dir}")
            return True

        except Exception as e:
            self.logger.error(f"设置输出目录失败: {e}")
            return False
    
    def init_driver(self):
        """初始化WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--start-maximized')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            self.driver.set_page_load_timeout(30)
            
            self.logger.info("WebDriver初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"WebDriver初始化失败: {e}")
            return False
    
    def save_text(self, content, filename):
        """保存文本"""
        try:
            filepath = os.path.join(self.output_dir, 'texts', filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            self.logger.info(f"文本保存成功: {filename}")
            return filepath
        except Exception as e:
            self.logger.error(f"文本保存失败: {e}")
            return None
    
    def extract_basic_info(self, soup):
        """提取基本产品信息"""
        info = []
        
        # 产品标题
        title_element = soup.find('h1') or soup.find('title')
        if title_element:
            title = title_element.get_text(strip=True)
            info.append(f"产品名称: {title}")
        
        # 价格信息
        page_text = soup.get_text()
        import re
        price_match = re.search(r'￥(\d+)', page_text)
        if price_match:
            price = price_match.group(1)
            info.append(f"产品价格: ￥{price}")
        
        # 产品特征
        info.append("\n产品特征:")
        feature_keywords = ['Wi-Fi', 'G网', '天线', '带机', '覆盖', 'M', 'AP', '双频']
        text_lines = page_text.split('\n')
        feature_count = 0
        
        for line in text_lines:
            line = line.strip()
            if (any(keyword in line for keyword in feature_keywords) and 
                len(line) > 10 and len(line) < 200 and 
                feature_count < 5):
                info.append(f"• {line}")
                feature_count += 1
        
        info.append(f"\n提取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        info.append(f"页面URL: {self.product_url}")
        
        return '\n'.join(info)
    
    def capture_full_page_screenshot(self):
        """获取完整页面长截图 - 优化拼接算法和连接稳定性"""
        try:
            self.logger.info("正在获取完整页面长截图...")

            # 设置窗口大小
            self.driver.set_window_size(1920, 1080)
            time.sleep(2)

            # 滚动到页面顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(3)

            # 获取页面总高度
            total_height = self.driver.execute_script("return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)")
            viewport_height = self.driver.execute_script("return window.innerHeight")

            self.logger.info(f"页面总高度: {total_height}px, 视口高度: {viewport_height}px")

            # 优化的分段截图策略
            screenshots = []
            current_position = 0
            scroll_step = viewport_height - 50
            max_retries = 3

            while current_position < total_height:
                retry_count = 0
                screenshot_success = False

                while retry_count < max_retries and not screenshot_success:
                    try:
                        # 检查WebDriver连接状态
                        self.driver.current_url  # 测试连接

                        # 滚动到当前位置
                        self.driver.execute_script(f"window.scrollTo(0, {current_position});")
                        time.sleep(2)

                        # 等待页面稳定
                        self.driver.execute_script("return document.readyState") == "complete"

                        # 截图
                        screenshot_data = self.driver.get_screenshot_as_png()
                        screenshot_image = Image.open(io.BytesIO(screenshot_data))

                        # 记录实际滚动位置
                        actual_scroll_top = self.driver.execute_script("return window.pageYOffset")
                        screenshots.append((screenshot_image, actual_scroll_top))

                        self.logger.info(f"截图片段 {len(screenshots)}: 目标位置 {current_position}px, 实际位置 {actual_scroll_top}px")

                        screenshot_success = True

                        # 检查是否已经到达页面底部
                        if actual_scroll_top + viewport_height >= total_height:
                            self.logger.info("已到达页面底部")
                            break

                    except (WebDriverException, ConnectionResetError) as e:
                        retry_count += 1
                        self.logger.warning(f"截图失败，重试 {retry_count}/{max_retries}: {e}")

                        if retry_count < max_retries:
                            # 等待一段时间后重试
                            time.sleep(5)
                            try:
                                # 尝试重新连接
                                self.driver.refresh()
                                time.sleep(3)
                            except:
                                self.logger.error("无法重新连接WebDriver")
                                break
                        else:
                            self.logger.error(f"截图重试失败，跳过位置 {current_position}px")
                            break

                if not screenshot_success:
                    self.logger.warning(f"跳过位置 {current_position}px 的截图")

                current_position += scroll_step

                # 防止无限循环
                if len(screenshots) > 50:
                    self.logger.warning("截图片段过多，停止截图")
                    break

                # 如果到达页面底部，退出循环
                if current_position >= total_height:
                    break

            # 拼接图片
            if screenshots:
                return self.stitch_screenshots(screenshots, total_height)
            else:
                return None

        except Exception as e:
            self.logger.error(f"页面截图失败: {e}")
            return None
    
    def stitch_screenshots(self, screenshots, total_height):
        """拼接截图片段"""
        try:
            self.logger.info(f"开始拼接 {len(screenshots)} 个截图片段...")
            
            if not screenshots:
                return None
            
            # 获取第一张图片的宽度
            first_image = screenshots[0][0]
            width = first_image.width
            
            # 创建长图
            long_image = Image.new('RGB', (width, total_height), 'white')
            
            # 优化的拼接算法 - 避免重叠错误
            last_bottom = 0
            for i, (image, scroll_position) in enumerate(screenshots):
                if i == 0:
                    # 第一张图片完整粘贴
                    long_image.paste(image, (0, 0))
                    last_bottom = image.height
                    self.logger.info(f"拼接片段 1/{len(screenshots)} - 位置: (0, 0)")
                else:
                    # 计算实际粘贴位置，避免重叠
                    expected_y = scroll_position
                    actual_y = max(last_bottom - 50, expected_y)  # 允许50px重叠用于平滑过渡

                    # 确保不超出边界
                    if actual_y + image.height > total_height:
                        crop_height = total_height - actual_y
                        if crop_height > 0:
                            image = image.crop((0, 0, image.width, crop_height))
                        else:
                            break

                    long_image.paste(image, (0, int(actual_y)))
                    last_bottom = actual_y + image.height
                    self.logger.info(f"拼接片段 {i+1}/{len(screenshots)} - 位置: (0, {int(actual_y)})")

            # 保存拼接后的图片
            screenshot_path = os.path.join(self.output_dir, 'images', 'full_page_screenshot.png')
            long_image.save(screenshot_path, 'PNG', optimize=True)
            
            file_size = os.path.getsize(screenshot_path)
            self.logger.info(f"完整页面长截图保存成功: full_page_screenshot.png ({file_size/1024/1024:.1f} MB)")
            
            return screenshot_path
            
        except Exception as e:
            self.logger.error(f"拼接截图失败: {e}")
            return None
    
    def extract_tech_specifications(self):
        """提取技术参数信息"""
        try:
            self.logger.info("开始提取技术参数信息...")
            
            # 1. 先滚动到页面底部，触发懒加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)

            # 2. 尝试点击技术参数标签（扩展选择器，优先级排序）
            tech_tab_selectors = [
                "//a[contains(text(), '查看全部参数')]",  # 优先点击"查看全部参数"
                "//a[contains(text(), '全部参数')]",
                "//button[contains(text(), '查看全部参数')]",
                "//div[contains(text(), '查看全部参数')]",
                "//a[contains(text(), '技术参数')]",
                "//div[contains(text(), '技术参数')]",
                "//span[contains(text(), '技术参数')]",
                "//a[contains(text(), '参数')]",
                "//div[contains(text(), '参数')]",
                "//a[contains(text(), '规格')]",
                "//div[contains(text(), '规格')]",
                "//a[contains(text(), '详细参数')]",
                "//div[contains(text(), '详细参数')]",
                "//a[contains(text(), '主要参数')]",
                "//div[contains(text(), '主要参数')]",
                "//button[contains(text(), '技术参数')]",
                "//button[contains(text(), '参数')]",
                "//li[contains(text(), '技术参数')]",
                "//li[contains(text(), '参数')]"
            ]

            clicked_tech_tab = False
            for selector in tech_tab_selectors:
                try:
                    wait = WebDriverWait(self.driver, 5)
                    element = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(2)
                    self.driver.execute_script("arguments[0].click();", element)
                    time.sleep(5)  # 增加等待时间让动态内容加载

                    self.logger.info(f"成功点击技术参数标签: {selector}")
                    clicked_tech_tab = True
                    break
                except Exception:
                    continue

            # 3. 如果没有找到标签，尝试查找展开按钮
            if not clicked_tech_tab:
                self.logger.warning("未找到技术参数标签，尝试查找展开按钮...")

                expand_selectors = [
                    "//button[contains(@class, 'expand')]",
                    "//div[contains(@class, 'expand')]",
                    "//a[contains(@class, 'more')]",
                    "//button[contains(text(), '更多')]",
                    "//a[contains(text(), '更多')]",
                    "//div[contains(text(), '展开')]",
                    "//span[contains(text(), '展开')]"
                ]

                for selector in expand_selectors:
                    try:
                        element = self.driver.find_element(By.XPATH, selector)
                        self.driver.execute_script("arguments[0].click();", element)
                        time.sleep(3)
                        self.logger.info(f"点击展开按钮: {selector}")
                        break
                    except:
                        continue

            # 4. 再次滚动确保内容加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            
            # 获取页面内容并提取技术参数
            content = self.driver.page_source
            soup = BeautifulSoup(content, 'lxml')
            
            return self.extract_tech_specs_text(soup)
            
        except Exception as e:
            self.logger.error(f"提取技术参数失败: {e}")
            return None

    def extract_tech_specs_text(self, soup):
        """从页面中提取技术参数文本 - 增强版表格解析"""
        try:
            specifications = []

            # 1. 查找技术参数表格（增强版）
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        param_name = cells[0].get_text(strip=True)
                        param_value = cells[1].get_text(strip=True)

                        # 检查是否是技术参数
                        if (param_name and param_value and
                            any(keyword in (param_name + param_value) for keyword in
                                ['频率', '速率', '天线', '功率', 'Wi-Fi', 'AP', 'MHz', 'GHz', 'Mbps', 'Gbps', 'dBi', 'W', 'IEEE', '工作温度', '协议', '端口', '尺寸', '重量'])):

                            # 清理参数名
                            param_name = param_name.rstrip('：:')
                            specifications.append(f"{param_name}：{param_value}")

            # 2. 查找网格布局的参数（常见于现代网页）
            # 查找包含参数的div容器
            param_containers = soup.find_all(['div', 'section'], class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['param', 'spec', 'tech', 'detail', 'info', 'grid', 'row', 'col']
            ))

            for container in param_containers:
                # 查找容器内的参数项
                param_items = container.find_all(['div', 'span', 'p'], class_=lambda x: x and any(
                    keyword in x.lower() for keyword in ['item', 'row', 'field', 'param', 'spec']
                ))

                for item in param_items:
                    item_text = item.get_text(strip=True)
                    if item_text and ('：' in item_text or ':' in item_text):
                        # 标准化分隔符
                        if '：' in item_text:
                            parts = item_text.split('：', 1)
                        else:
                            parts = item_text.split(':', 1)

                        if len(parts) == 2:
                            param_name = parts[0].strip()
                            param_value = parts[1].strip()
                            if param_name and param_value:
                                specifications.append(f"{param_name}：{param_value}")

            # 3. 查找并行布局的参数（左右分栏）
            # 查找可能的参数名和参数值配对
            all_divs = soup.find_all('div')
            for i, div in enumerate(all_divs):
                div_text = div.get_text(strip=True)

                # 如果这个div看起来像参数名
                if (div_text and len(div_text) < 50 and
                    any(keyword in div_text for keyword in ['WAN', 'LAN', '口数', '带宽', '功能', '温度', '湿度', '协议', '支持', '接口'])):

                    # 查找相邻的div作为参数值
                    for j in range(i+1, min(i+5, len(all_divs))):  # 查找后续4个div
                        next_div = all_divs[j]
                        next_text = next_div.get_text(strip=True)

                        if (next_text and len(next_text) < 200 and next_text != div_text and
                            any(keyword in next_text for keyword in ['个', 'Mbps', '支持', '不支持', '℃', '%', 'mm', 'IEEE', 'MHz', 'GHz'])):

                            specifications.append(f"{div_text}：{next_text}")
                            break

            # 4. 查找定义列表格式
            dl_elements = soup.find_all('dl')
            for dl in dl_elements:
                dt_elements = dl.find_all('dt')
                dd_elements = dl.find_all('dd')

                for dt, dd in zip(dt_elements, dd_elements):
                    param_name = dt.get_text(strip=True).rstrip('：:')
                    param_value = dd.get_text(strip=True)
                    if param_name and param_value:
                        specifications.append(f"{param_name}：{param_value}")

            # 3. 智能提取技术参数（增强版）
            page_text = soup.get_text()
            lines = page_text.split('\n')

            # 扩展技术关键词
            tech_keywords = [
                '频率', '速率', '天线', '功率', 'MHz', 'GHz', 'Mbps', 'Gbps', 'dBi', 'W', 'IEEE',
                '工作温度', '协议', '端口', '尺寸', '重量', '支持', '标准', 'Wi-Fi', 'WAN', 'LAN',
                '接口', '带宽', '传输', '覆盖', '范围', '电源', '功耗', '认证', '加密', '安全',
                '处理器', '内存', '存储', '系统', '管理', '配置', '最大', '最小', '典型', '额定',
                '输入', '输出', '电压', '电流', '湿度', '海拔', '工作环境', '存储环境', '口数',
                'Mesh', 'APP', 'DHCP', 'DNS', 'IPV', '波束', '调整', '隔离', '成形'
            ]

            # 方法1: 直接从文本行提取参数对
            for line in lines:
                line = line.strip()
                if (any(keyword in line for keyword in tech_keywords) and
                    len(line) > 5 and len(line) < 200 and  # 控制长度
                    ('：' in line or ':' in line)):

                    # 标准化分隔符
                    if '：' in line:
                        parts = line.split('：', 1)
                    else:
                        parts = line.split(':', 1)

                    if len(parts) == 2:
                        param_name = parts[0].strip()
                        param_value = parts[1].strip()

                        # 过滤掉无效的参数
                        if (param_name and param_value and
                            len(param_name) < 50 and len(param_value) < 150 and
                            param_name != param_value and
                            not any(invalid in param_name.lower() for invalid in ['版权', '客服', '热线', '公司', '网站'])):
                            specifications.append(f"{param_name}：{param_value}")

            # 方法2: 查找相邻div的参数对（针对现代网页布局）
            all_divs = soup.find_all('div')
            for i, div in enumerate(all_divs):
                div_text = div.get_text(strip=True)

                # 如果这个div看起来像参数名
                if (div_text and len(div_text) < 50 and len(div_text) > 2 and
                    any(keyword in div_text for keyword in tech_keywords) and
                    '：' not in div_text and ':' not in div_text):  # 确保不是已经包含值的完整参数

                    # 查找相邻的div作为参数值
                    for j in range(i+1, min(i+5, len(all_divs))):
                        next_div = all_divs[j]
                        next_text = next_div.get_text(strip=True)

                        if (next_text and len(next_text) < 100 and next_text != div_text and
                            (any(keyword in next_text for keyword in ['个', 'Mbps', '支持', '不支持', '℃', '%', 'mm', 'IEEE', 'MHz', 'GHz', 'W']) or
                             next_text.replace(' ', '').replace('-', '').replace('~', '').replace('.', '').isdigit())):

                            specifications.append(f"{div_text}：{next_text}")
                            break

            # 方法3: 查找特殊格式的参数表格
            param_tables = soup.find_all(['table', 'div'], class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['param', 'spec', 'tech', 'detail', 'info']
            ))

            for table in param_tables:
                table_text = table.get_text()
                table_lines = table_text.split('\n')
                for line in table_lines:
                    line = line.strip()
                    if (line and ('：' in line or ':' in line) and
                        any(keyword in line for keyword in tech_keywords) and
                        len(line) < 200):

                        if '：' in line:
                            parts = line.split('：', 1)
                        else:
                            parts = line.split(':', 1)

                        if len(parts) == 2:
                            param_name = parts[0].strip()
                            param_value = parts[1].strip()
                            if param_name and param_value and len(param_name) < 50:
                                specifications.append(f"{param_name}：{param_value}")

            # 智能去重和格式化（增强版）
            if specifications:
                # 第一步：预处理和标准化
                processed_specs = []

                for spec in specifications:
                    spec = spec.strip()
                    if not spec or '：' not in spec:
                        continue

                    param_name, param_value = spec.split('：', 1)
                    param_name = param_name.strip()
                    param_value = param_value.strip()

                    # 过滤无效参数
                    if (not param_name or not param_value or
                        len(param_name) > 100 or len(param_value) > 300 or
                        param_name == param_value):
                        continue

                    # 清理参数名中的冗余信息
                    import re

                    # 移除参数名末尾的数值和单位（这些应该在参数值中）
                    param_name_clean = re.sub(r'[0-9]+\s*(个|Mbps|MHz|GHz|W|℃|%|mm|dBi)\s*$', '', param_name).strip()
                    if param_name_clean:
                        param_name = param_name_clean

                    # 移除参数名中的"不支持"、"支持"等状态词（这些应该在参数值中）
                    param_name_clean = re.sub(r'(不支持|支持)$', '', param_name).strip()
                    if param_name_clean:
                        param_name = param_name_clean

                    # 处理参数值中包含参数名的情况
                    # 例如："蓝牙支持蓝牙5.0：支持蓝牙5.0" -> "蓝牙：支持蓝牙5.0"
                    if param_name in param_value:
                        # 尝试提取核心参数名
                        if '蓝牙' in param_name:
                            param_name = '蓝牙'
                        elif 'QAM' in param_name:
                            param_name = 'QAM调制'
                        elif 'WiFi' in param_name or 'Wi-Fi' in param_name:
                            if '智能家居' in param_name:
                                param_name = '智能家居WiFi'
                            else:
                                param_name = re.sub(r'支持.*', '', param_name).strip()

                    # 清理过长的参数值，提取关键信息
                    if len(param_value) > 100:
                        # 如果参数值太长，尝试提取关键部分
                        if '支持' in param_value and '不支持' not in param_value:
                            param_value = '支持'
                        elif '不支持' in param_value:
                            param_value = '不支持'

                    # 标准化参数名
                    if len(param_name) > 30 and any(keyword in param_name for keyword in ['最大支持', '各', '带宽', '接口']):
                        if 'WAN口数量' in param_name:
                            param_name = 'WAN口数量'
                        elif 'LAN口数量' in param_name:
                            param_name = 'LAN口数量'
                        elif 'WAN口带宽' in param_name:
                            param_name = 'WAN口带宽'
                        elif 'LAN口带宽' in param_name:
                            param_name = 'LAN口带宽'

                    processed_specs.append((param_name, param_value))

                # 第二步：智能去重 - 选择最佳的参数值
                param_dict = {}

                for param_name, param_value in processed_specs:
                    param_key = param_name.lower().strip()

                    if param_key not in param_dict:
                        param_dict[param_key] = (param_name, param_value)
                    else:
                        # 如果已存在，选择更好的版本
                        existing_name, existing_value = param_dict[param_key]

                        # 优先选择更完整的参数值（包含数字和单位）
                        current_score = self._score_param_quality(param_name, param_value)
                        existing_score = self._score_param_quality(existing_name, existing_value)

                        # 特殊处理：如果参数名更简洁且参数值相同，优先选择简洁的
                        if param_value == existing_value and len(param_name) < len(existing_name):
                            param_dict[param_key] = (param_name, param_value)
                        elif current_score > existing_score:
                            param_dict[param_key] = (param_name, param_value)

                # 第三步：生成最终的去重列表
                unique_specs = []
                for param_name, param_value in param_dict.values():
                    unique_specs.append(f"{param_name}：{param_value}")

                # 保存到文件
                spec_file = os.path.join(self.output_dir, 'texts', 'tech_specifications.txt')
                with open(spec_file, 'w', encoding='utf-8') as f:
                    f.write("技术参数\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"提取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"页面URL: {self.product_url}\n")
                    f.write("=" * 50 + "\n\n")

                    for spec in unique_specs:
                        f.write(f"{spec}\n")

                self.logger.info(f"技术参数保存成功: tech_specifications.txt ({len(unique_specs)} 项)")
                return spec_file
            else:
                self.logger.warning("未找到技术参数信息")
                return None

        except Exception as e:
            self.logger.error(f"提取技术参数文本失败: {e}")
            return None

    def _score_param_quality(self, param_name, param_value):
        """评估参数质量，返回分数（越高越好）"""
        score = 0

        # 参数值质量评分
        if param_value:
            # 包含数字和单位的参数值更好
            import re
            if re.search(r'\d+\s*(个|Mbps|MHz|GHz|W|℃|%|mm|dBi|V|A)', param_value):
                score += 10

            # 明确的支持/不支持状态
            if param_value in ['支持', '不支持']:
                score += 5

            # 具体的数值
            if re.search(r'\d+', param_value):
                score += 3

            # 避免重复的参数名
            if param_name.lower() not in param_value.lower():
                score += 2

            # 参数值长度适中
            if 2 <= len(param_value) <= 50:
                score += 1

        # 参数名质量评分
        if param_name:
            # 简洁的参数名更好
            if len(param_name) <= 20:
                score += 2

            # 避免包含状态词的参数名
            if not any(word in param_name for word in ['支持', '不支持']):
                score += 1

        return score

    def extract_product_name(self):
        """提取产品名称"""
        try:
            # 尝试多种选择器来获取产品名称
            name_selectors = [
                'h1',
                '.product-title',
                '.product-name',
                '.title',
                '[class*="title"]',
                '[class*="name"]'
            ]

            for selector in name_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    name = element.text.strip()
                    if name and len(name) < 100:  # 合理的产品名称长度
                        self.logger.info(f"提取到产品名称: {name}")
                        return name
                except:
                    continue

            # 如果都没找到，尝试从页面标题获取
            try:
                title = self.driver.title
                if title:
                    # 清理标题，移除网站名称等
                    name = title.split('-')[0].split('|')[0].split('_')[0].strip()
                    if name:
                        self.logger.info(f"从页面标题提取产品名称: {name}")
                        return name
            except:
                pass

            # 最后尝试从URL获取
            try:
                from urllib.parse import urlparse
                path = urlparse(self.product_url).path
                name = path.split('/')[-1].replace('.html', '').replace('.htm', '')
                if name:
                    self.logger.info(f"从URL提取产品名称: {name}")
                    return name
            except:
                pass

            return "Unknown_Product"

        except Exception as e:
            self.logger.error(f"提取产品名称失败: {e}")
            return "Unknown_Product"

    def download_page_images(self):
        """下载页面中的所有大图"""
        try:
            self.logger.info("开始下载页面中的大图...")

            # 查找页面中的所有图片
            img_elements = self.driver.find_elements(By.TAG_NAME, "img")

            downloaded_images = []
            image_urls = set()  # 用于去重

            for img in img_elements:
                try:
                    # 获取图片URL
                    img_url = img.get_attribute("src")
                    if not img_url:
                        img_url = img.get_attribute("data-src")  # 懒加载图片

                    if not img_url or img_url in image_urls:
                        continue

                    # 转换为绝对URL
                    if img_url.startswith('//'):
                        img_url = 'https:' + img_url
                    elif img_url.startswith('/'):
                        img_url = urljoin(self.product_url, img_url)

                    # 移除压缩参数，获取原图
                    if '?x-oss-process=' in img_url:
                        img_url = img_url.split('?x-oss-process=')[0]
                        self.logger.info(f"移除压缩参数，获取原图: {img_url}")

                    # 检查图片尺寸，只下载大图
                    try:
                        width = img.get_attribute("width") or img.get_attribute("naturalWidth")
                        height = img.get_attribute("height") or img.get_attribute("naturalHeight")

                        if width and height:
                            width, height = int(width), int(height)
                            # 只下载较大的图片 (宽度>300px 或 高度>200px)
                            if width < 300 and height < 200:
                                continue
                    except:
                        pass

                    image_urls.add(img_url)

                    # 下载图片
                    response = requests.get(img_url, timeout=10, headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    })

                    if response.status_code == 200:
                        # 验证是否为有效图片
                        try:
                            img_data = Image.open(io.BytesIO(response.content))
                            if img_data.width > 300 or img_data.height > 200:
                                downloaded_images.append((img_data, img_url))
                                self.logger.info(f"下载图片: {img_url} ({img_data.width}x{img_data.height})")
                        except Exception as e:
                            self.logger.warning(f"图片格式验证失败: {img_url} - {e}")
                            continue

                except Exception as e:
                    self.logger.warning(f"下载图片失败: {e}")
                    continue

            # 拼接下载的图片
            if downloaded_images:
                return self.stitch_downloaded_images(downloaded_images)
            else:
                self.logger.warning("没有找到可下载的大图")
                return None

        except Exception as e:
            self.logger.error(f"下载页面图片失败: {e}")
            return None

    def stitch_downloaded_images(self, images):
        """拼接下载的图片"""
        try:
            if not images:
                return None

            self.logger.info(f"开始拼接 {len(images)} 张下载的图片...")

            # 按图片宽度排序，选择最常见的宽度作为基准
            widths = [img[0].width for img in images]
            most_common_width = max(set(widths), key=widths.count)

            # 调整图片尺寸并计算总高度
            processed_images = []
            total_height = 0

            for img_data, img_url in images:
                # 如果宽度不一致，按比例缩放
                if img_data.width != most_common_width:
                    ratio = most_common_width / img_data.width
                    new_height = int(img_data.height * ratio)
                    img_data = img_data.resize((most_common_width, new_height), Image.Resampling.LANCZOS)

                processed_images.append(img_data)
                total_height += img_data.height

            # 创建拼接图片
            stitched_image = Image.new('RGB', (most_common_width, total_height), 'white')

            current_y = 0
            for img in processed_images:
                stitched_image.paste(img, (0, current_y))
                current_y += img.height

            # 保存拼接图片
            stitched_path = os.path.join(self.output_dir, 'images', 'stitched_images.png')
            stitched_image.save(stitched_path, 'PNG', optimize=True)

            file_size = os.path.getsize(stitched_path)
            self.logger.info(f"图片拼接完成: stitched_images.png ({file_size/1024/1024:.1f} MB)")

            return stitched_path

        except Exception as e:
            self.logger.error(f"拼接图片失败: {e}")
            return None

    def crawl(self):
        """执行完整的爬取流程"""
        self.logger.info("开始爬取产品信息...")

        try:
            # 初始化WebDriver
            if not self.init_driver():
                return False

            # 访问页面
            self.driver.get(self.product_url)
            time.sleep(5)

            # 提取产品名称并设置输出目录
            self.product_name = self.extract_product_name()
            if not self.setup_output_directory(self.product_name):
                return False

            # 获取页面内容
            content = self.driver.page_source
            soup = BeautifulSoup(content, 'lxml')

            # 1. 提取基本信息
            basic_info = self.extract_basic_info(soup)
            self.save_text(basic_info, 'product_info.txt')

            # 2. 获取完整页面截图
            screenshot_path = self.capture_full_page_screenshot()

            # 3. 下载页面大图并拼接
            stitched_images_path = self.download_page_images()

            # 4. 提取技术参数
            tech_specs_file = self.extract_tech_specifications()
            tech_specs_success = tech_specs_file is not None

            # 5. 生成报告
            self.generate_report(screenshot_path, stitched_images_path, tech_specs_success)

            self.logger.info("爬取完成")
            return True

        except Exception as e:
            self.logger.error(f"爬取失败: {e}")
            return False

        finally:
            if self.driver:
                self.driver.quit()

    def generate_report(self, screenshot_path, stitched_images_path, tech_specs):
        """生成爬取报告"""
        try:
            report = "=== 锐捷产品爬取报告 ===\n\n"
            report += f"爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            report += f"产品页面: {self.product_url}\n\n"

            # 检查生成的文件
            report += "=== 生成文件 ===\n"

            # 文本文件
            product_info_path = os.path.join(self.output_dir, 'texts', 'product_info.txt')
            if os.path.exists(product_info_path):
                size = os.path.getsize(product_info_path)
                report += f"✅ product_info.txt ({size} 字节)\n"

            tech_specs_path = os.path.join(self.output_dir, 'texts', 'tech_specifications.txt')
            if os.path.exists(tech_specs_path):
                size = os.path.getsize(tech_specs_path)
                report += f"✅ tech_specifications.txt ({size} 字节)\n"

            # 图片文件
            if screenshot_path and os.path.exists(screenshot_path):
                size = os.path.getsize(screenshot_path)
                report += f"✅ full_page_screenshot.png ({size/1024/1024:.1f} MB)\n"

            # 拼接图片文件
            if stitched_images_path and os.path.exists(stitched_images_path):
                size = os.path.getsize(stitched_images_path)
                report += f"✅ stitched_images.png ({size/1024/1024:.1f} MB)\n"

            report += "\n=== 功能说明 ===\n"
            report += "1. product_info.txt - 产品基本信息（名称、价格、特征）\n"
            report += "2. tech_specifications.txt - 技术参数详细信息\n"
            report += "3. full_page_screenshot.png - 完整页面长截图\n"
            report += "4. stitched_images.png - 页面大图拼接（如果有）\n"

            # 技术参数提取状态
            if tech_specs:
                report += "\n✅ 技术参数提取成功\n"
            else:
                report += "\n⚠️ 技术参数提取不完整\n"

            self.save_text(report, 'crawl_report.txt')

        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")


def get_product_url():
    """获取产品URL"""
    import sys

    if len(sys.argv) > 1:
        return sys.argv[1]

    print("=== 锐捷产品爬虫 ===")
    print("请输入要爬取的产品页面URL，或直接回车使用默认URL")
    print("默认URL: https://www.ruijiery.com/product/1796066620307464193.html?key=1")

    user_input = input("请输入产品URL: ").strip()

    if user_input:
        return user_input
    else:
        return "https://www.ruijiery.com/product/1796066620307464193.html?key=1"


if __name__ == "__main__":
    try:
        # 获取产品URL
        product_url = get_product_url()

        print(f"\n🎯 目标URL: {product_url}")
        print("🚀 开始爬取...")

        # 创建爬虫实例
        crawler = RuijieCrawler(product_url)

        # 执行爬取
        success = crawler.crawl()

        if success:
            print("\n✅ 爬取完成！")
            print("📁 请查看 output 目录下的文件：")
            print("   📄 texts/product_info.txt - 产品基本信息")
            print("   📄 texts/tech_specifications.txt - 技术参数")
            print("   📄 texts/crawl_report.txt - 爬取报告")
            print("   🖼️ images/full_page_screenshot.png - 完整页面截图")
        else:
            print("❌ 爬取失败，请查看日志信息")

    except KeyboardInterrupt:
        print("\n⏹️ 用户取消操作")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
