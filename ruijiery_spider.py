import os
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from PIL import Image
from io import BytesIO

URL = "https://www.ruijiery.com/product/1925441870023266305.html"
SAVE_DIR = "ruijiery_data"
os.makedirs(SAVE_DIR, exist_ok=True)

def save_text(filename, text):
    with open(os.path.join(SAVE_DIR, filename), "w", encoding="utf-8") as f:
        f.write(text)

def save_image(filename, url):
    try:
        resp = requests.get(url)
        img = Image.open(BytesIO(resp.content))
        img.save(os.path.join(SAVE_DIR, filename))
    except Exception as e:
        print(f"图片保存失败: {url} 错误: {e}")

def get_main_info(driver):
    driver.get(URL)
    time.sleep(2)
    soup = BeautifulSoup(driver.page_source, "html.parser")
    # 产品标题
    title = soup.find("h1", class_="product-title")
    # 价格
    price = soup.find("span", class_="product-price")
    # 缩略图
    thumb_img = soup.find("img", class_="product-main-img")
    # 简要描述
    desc = soup.find("div", class_="product-desc")
    text = f"标题: {title.text.strip() if title else ''}\n价格: {price.text.strip() if price else ''}\n描述: {desc.text.strip() if desc else ''}"
    save_text("main_info.txt", text)
    if thumb_img and thumb_img.get("src"):
        save_image("thumbnail.jpg", thumb_img["src"])

def get_tab_info(driver, tab_text, txt_name, img_prefix):
    # 点击指定tab
    tabs = driver.find_elements(By.CSS_SELECTOR, ".product-detail-tab li")
    for tab in tabs:
        if tab_text in tab.text:
            tab.click()
            time.sleep(2)
            break
    soup = BeautifulSoup(driver.page_source, "html.parser")
    # 获取tab内容
    tab_content = soup.find("div", class_="product-detail-content")
    text = tab_content.get_text(separator="\n", strip=True) if tab_content else ""
    save_text(txt_name, text)
    # 获取图片
    imgs = tab_content.find_all("img") if tab_content else []
    for idx, img in enumerate(imgs):
        src = img.get("src")
        if src and src.startswith("http"):
            save_image(f"{img_prefix}_{idx+1}.jpg", src)

def main():
    # 启动浏览器
    service = Service()
    options = webdriver.ChromeOptions()
    options.add_argument("--headless")
    driver = webdriver.Chrome(service=service, options=options)
    try:
        get_main_info(driver)
        get_tab_info(driver, "商品详情", "detail.txt", "detail_img")
        get_tab_info(driver, "技术参数", "spec.txt", "spec_img")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()