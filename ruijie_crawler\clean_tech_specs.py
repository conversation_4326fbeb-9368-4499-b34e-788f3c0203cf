#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
技术参数后处理清理工具
进一步去重和格式化技术参数
"""

import re
import sys
import os

def clean_tech_specifications(file_path):
    """清理技术参数文件"""
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    # 读取原文件
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 分离头部和参数内容
    header_lines = []
    param_lines = []
    
    in_params = False
    for line in lines:
        line = line.strip()
        if line.startswith('='):
            header_lines.append(line)
            in_params = True
        elif not in_params or not line:
            header_lines.append(line)
        else:
            if '：' in line:
                param_lines.append(line)
    
    # 处理参数
    cleaned_params = {}
    
    for line in param_lines:
        if '：' not in line:
            continue
            
        param_name, param_value = line.split('：', 1)
        param_name = param_name.strip()
        param_value = param_value.strip()
        
        if not param_name or not param_value:
            continue
        
        # 清理参数名
        param_name = clean_param_name(param_name)
        param_value = clean_param_value(param_value)
        
        if not param_name or not param_value:
            continue
        
        # 去重逻辑
        param_key = param_name.lower()
        
        if param_key not in cleaned_params:
            cleaned_params[param_key] = (param_name, param_value)
        else:
            # 选择更好的版本
            existing_name, existing_value = cleaned_params[param_key]
            
            # 优先选择更简洁的参数名
            if len(param_name) < len(existing_name) and param_value == existing_value:
                cleaned_params[param_key] = (param_name, param_value)
            # 优先选择更完整的参数值
            elif score_param_value(param_value) > score_param_value(existing_value):
                cleaned_params[param_key] = (param_name, param_value)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        # 写入头部
        for line in header_lines:
            f.write(line + '\n')
        
        f.write('\n')
        
        # 写入清理后的参数
        for param_name, param_value in cleaned_params.values():
            f.write(f"{param_name}：{param_value}\n")
    
    print(f"清理完成: {len(cleaned_params)} 个参数")

def clean_param_name(name):
    """清理参数名"""
    # 移除数值和单位
    name = re.sub(r'[0-9]+\s*(个|Mbps|MHz|GHz|W|℃|%|mm|dBi|V|A)\s*$', '', name).strip()
    
    # 移除状态词
    name = re.sub(r'(不支持|支持)$', '', name).strip()
    
    # 移除重复的描述
    if '支持' in name and len(name) > 10:
        # 提取核心名称
        if '蓝牙' in name:
            name = '蓝牙'
        elif 'QAM' in name:
            name = 'QAM调制'
        elif 'WiFi' in name or 'Wi-Fi' in name:
            if '智能家居' in name:
                name = '智能家居WiFi'
            elif '公寓网络' in name:
                name = '公寓网络SSID'
    
    # 标准化常见参数名
    name_mappings = {
        '最大支持WAN口数量': 'WAN口数量',
        '最大支持LAN口数量': 'LAN口数量',
        '最大WAN口形态最大支持WAN口数量': 'WAN口数量',
        '最大LAN口形态最大支持LAN口数量': 'LAN口数量',
    }
    
    for old_name, new_name in name_mappings.items():
        if old_name in name:
            name = new_name
            break
    
    return name

def clean_param_value(value):
    """清理参数值"""
    # 如果参数值太长，尝试简化
    if len(value) > 100:
        if '支持' in value and '不支持' not in value:
            return '支持'
        elif '不支持' in value:
            return '不支持'
    
    return value

def score_param_value(value):
    """评估参数值质量"""
    score = 0
    
    # 包含数字和单位
    if re.search(r'\d+\s*(个|Mbps|MHz|GHz|W|℃|%|mm|dBi|V|A)', value):
        score += 10
    
    # 明确的状态
    if value in ['支持', '不支持']:
        score += 5
    
    # 包含数字
    if re.search(r'\d+', value):
        score += 3
    
    # 长度适中
    if 2 <= len(value) <= 50:
        score += 2
    
    return score

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python clean_tech_specs.py <tech_specifications.txt文件路径>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    clean_tech_specifications(file_path)
