#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TP-Link规格参数格式化工具
将连续的参数文本格式化为易读的结构
"""

import re
import sys
import os

def format_tplink_specs(input_file, output_file):
    """格式化TP-Link规格参数"""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取规格参数文本（跳过头部信息）
    lines = content.split('\n')
    spec_text = ""
    for line in lines:
        if '硬件规格' in line:
            spec_text = line
            break
    
    if not spec_text:
        print("未找到规格参数文本")
        return
    
    # 定义分类关键词和对应的标题
    categories = [
        ('硬件规格', '硬件规格'),
        ('Wi-Fi', 'Wi-Fi'),
        ('IEEE 802.11', 'Wi-Fi协议'),
        ('最高无线速率', '无线速率'),
        ('波束成形', '无线技术'),
        ('以太网端口', '以太网端口'),
        ('USB端口', 'USB端口'),
        ('尺寸重量', '尺寸重量'),
        ('裸机尺寸', '尺寸规格'),
        ('天线类型', '天线类型'),
        ('软件功能', '软件功能'),
        ('管理', '管理功能'),
        ('安全', '安全功能'),
        ('其他', '其他'),
        ('电源', '电源'),
        ('包装清单', '包装清单')
    ]
    
    # 格式化输出
    formatted_specs = []
    formatted_specs.append("技术参数（格式化）")
    formatted_specs.append("=" * 50)
    formatted_specs.append(f"原始文件: {input_file}")
    formatted_specs.append("=" * 50)
    formatted_specs.append("")
    
    # 按分类提取和格式化
    remaining_text = spec_text
    
    for keyword, title in categories:
        if keyword in remaining_text:
            # 查找该分类的内容
            start_pos = remaining_text.find(keyword)
            if start_pos != -1:
                # 查找下一个分类的开始位置
                next_pos = len(remaining_text)
                for next_keyword, _ in categories:
                    if next_keyword != keyword:
                        temp_pos = remaining_text.find(next_keyword, start_pos + len(keyword))
                        if temp_pos != -1 and temp_pos < next_pos:
                            next_pos = temp_pos
                
                # 提取该分类的内容
                section_text = remaining_text[start_pos:next_pos].strip()
                
                if section_text:
                    formatted_specs.append(f"=== {title} ===")
                    
                    # 进一步格式化该分类的内容
                    if keyword == 'Wi-Fi' or keyword == 'IEEE 802.11':
                        # Wi-Fi相关参数
                        wifi_params = [
                            'IEEE 802.11a/b/g/n/ac/ax无线协议',
                            '最高无线速率6579Mbps（2.4GHz 574Mbps，5GHz频段1 4804Mbps，5GHz频段2 1201Mbps）',
                            '波束成形（Beamforming）',
                            'MU-MIMO',
                            'Wi-Fi多频合一',
                            '易展功能'
                        ]
                        for param in wifi_params:
                            if param.replace('（', '').replace('）', '') in section_text:
                                formatted_specs.append(f"  • {param}")
                    
                    elif keyword == '以太网端口':
                        # 端口相关参数
                        port_params = [
                            '1个10Gbps SFP+口',
                            '1个2.5Gbps以太网接口',
                            '3个千兆以太网接口',
                            '(以上均支持WAN/LAN盲插)'
                        ]
                        for param in port_params:
                            if param.replace('(', '').replace(')', '') in section_text:
                                formatted_specs.append(f"  • {param}")
                    
                    elif keyword == 'USB端口':
                        formatted_specs.append("  • 1个USB 3.0接口")
                    
                    elif keyword == '裸机尺寸':
                        # 尺寸重量参数
                        size_params = [
                            '裸机尺寸（长×宽×高）：198×87×261mm',
                            '包装尺寸（长×宽×高）：419.5×263.5×119.5mm',
                            '裸机重量：1.06kg（单台，不含配件、包装）',
                            '整机重量：1.35kg（单台，含配件、包装）'
                        ]
                        for param in size_params:
                            if '198×87×261mm' in section_text or '419.5×263.5×119.5mm' in section_text or '1.06kg' in section_text or '1.35kg' in section_text:
                                formatted_specs.append(f"  • {param}")
                                break
                    
                    elif keyword == '天线类型':
                        formatted_specs.append("  • 内置全向天线")
                    
                    elif keyword == '软件功能':
                        # 软件功能参数
                        software_params = [
                            '信号调节', '终端管理', 'IP与MAC地址绑定', 'AP隔离', '虚拟服务器',
                            '路由/AP（有线中继）/桥接（无线中继）模式',
                            '端口功能自定义（双WAN口/IPTV口/端口聚合口/游戏专用口）',
                            '频段功能自定义（游戏频段/访客频段）',
                            '行为管控（应用行为限制/网站访问限制/网页上传下载限制）',
                            'VPN功能（支持IPSec、L2TP、PPTP）',
                            '动态域名服务（DDNS）', 'DMZ主机', '无线定时开关',
                            '自动清理', '文件共享', '本地在线/本地固件/远程升级'
                        ]
                        for param in software_params:
                            if param.replace('（', '').replace('）', '') in section_text:
                                formatted_specs.append(f"  • {param}")
                    
                    elif keyword == '管理':
                        formatted_specs.append("  • 电脑/手机/Pad Web管理")
                        formatted_specs.append("  • 手机APP管理")
                    
                    elif keyword == '安全':
                        security_params = [
                            'WPA-PSK、WPA2-PSK、WPA3无线加密',
                            '主人网络、访客网络',
                            '管理员身份限定'
                        ]
                        for param in security_params:
                            if param.replace('、', '') in section_text:
                                formatted_specs.append(f"  • {param}")
                    
                    elif keyword == '电源':
                        formatted_specs.append("  • 直流供电，出厂配送12V/4A电源适配器")
                    
                    elif keyword == '包装清单':
                        package_params = [
                            '路由器1个',
                            '电源适配器12V/4A 1个',
                            '快速安装指南1份'
                        ]
                        for param in package_params:
                            if param in section_text:
                                formatted_specs.append(f"  • {param}")
                    
                    formatted_specs.append("")
    
    # 保存格式化后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in formatted_specs:
            f.write(line + '\n')
    
    print(f"格式化完成: {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python format_tplink_specs.py <输入文件> <输出文件>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        sys.exit(1)
    
    format_tplink_specs(input_file, output_file)
