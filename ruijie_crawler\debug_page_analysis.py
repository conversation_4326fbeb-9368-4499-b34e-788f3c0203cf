#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
页面结构分析工具
用于调试和分析网页的技术参数提取问题
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

def analyze_page(url):
    """分析页面结构"""
    
    # 初始化WebDriver
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        print(f"正在分析页面: {url}")
        driver.get(url)
        time.sleep(5)
        
        # 1. 查找所有可能的标签页
        print("\n=== 查找标签页 ===")
        tab_selectors = [
            "//a[contains(text(), '技术参数')]",
            "//div[contains(text(), '技术参数')]", 
            "//span[contains(text(), '技术参数')]",
            "//a[contains(text(), '参数')]",
            "//div[contains(text(), '参数')]",
            "//a[contains(text(), '规格')]",
            "//div[contains(text(), '规格')]",
            "//a[contains(text(), '详细')]",
            "//div[contains(text(), '详细')]",
            "//button[contains(text(), '技术')]",
            "//button[contains(text(), '参数')]"
        ]
        
        found_tabs = []
        for selector in tab_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    text = element.text.strip()
                    if text:
                        found_tabs.append((selector, text))
                        print(f"找到标签: {selector} -> {text}")
            except:
                pass
        
        # 2. 查找所有表格
        print("\n=== 查找表格 ===")
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            try:
                table_text = table.text.strip()
                if table_text and len(table_text) > 20:
                    print(f"表格 {i+1}: {table_text[:100]}...")
            except:
                pass
        
        # 3. 查找包含参数的div
        print("\n=== 查找参数容器 ===")
        param_divs = driver.find_elements(By.XPATH, "//div[contains(@class, 'param') or contains(@class, 'spec') or contains(@class, 'tech')]")
        print(f"找到 {len(param_divs)} 个参数容器")
        
        for i, div in enumerate(param_divs):
            try:
                div_text = div.text.strip()
                if div_text and len(div_text) > 10:
                    print(f"参数容器 {i+1}: {div_text[:100]}...")
            except:
                pass
        
        # 4. 分析页面源码中的关键词
        print("\n=== 分析页面内容 ===")
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'lxml')
        
        # 查找包含技术关键词的文本
        tech_keywords = ['MHz', 'GHz', 'Mbps', 'Gbps', 'dBi', 'IEEE', 'Wi-Fi', 'WAN', 'LAN', '接口', '频率', '功率']
        
        all_text = soup.get_text()
        lines = all_text.split('\n')
        
        param_lines = []
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in tech_keywords) and len(line) > 10 and len(line) < 200:
                param_lines.append(line)
        
        print(f"找到 {len(param_lines)} 行包含技术关键词的文本:")
        for line in param_lines[:20]:  # 只显示前20行
            print(f"  {line}")
        
        # 5. 查找图片中的参数（通过alt和title属性）
        print("\n=== 查找图片参数 ===")
        images = driver.find_elements(By.TAG_NAME, "img")
        for img in images:
            alt = img.get_attribute("alt") or ""
            title = img.get_attribute("title") or ""
            src = img.get_attribute("src") or ""
            
            if any(keyword in (alt + title) for keyword in tech_keywords):
                print(f"图片参数: alt='{alt}', title='{title}', src='{src[:50]}...'")
        
        # 6. 尝试点击可能的标签并查看内容变化
        print("\n=== 尝试点击标签 ===")
        for selector, text in found_tabs[:3]:  # 只尝试前3个
            try:
                print(f"尝试点击: {text}")
                element = driver.find_element(By.XPATH, selector)
                driver.execute_script("arguments[0].click();", element)
                time.sleep(3)
                
                # 检查页面内容是否有变化
                new_content = driver.page_source
                new_soup = BeautifulSoup(new_content, 'lxml')
                new_text = new_soup.get_text()
                new_lines = new_text.split('\n')
                
                new_param_lines = []
                for line in new_lines:
                    line = line.strip()
                    if any(keyword in line for keyword in tech_keywords) and len(line) > 10 and len(line) < 200:
                        new_param_lines.append(line)
                
                print(f"点击后找到 {len(new_param_lines)} 行技术参数")
                if len(new_param_lines) > len(param_lines):
                    print("发现新的参数内容!")
                    for line in new_param_lines[len(param_lines):len(param_lines)+10]:
                        print(f"  新增: {line}")
                
            except Exception as e:
                print(f"点击失败: {e}")
        
        print("\n=== 分析完成 ===")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    url = "https://www.ruijiery.com/product/1925441870023266305.html"
    analyze_page(url)
