#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TP-Link批量爬虫
支持从文件读取多个URL进行批量爬取
"""

import os
import time
import logging
from tplink_crawler import TPLinkCrawler

class TPLinkBatchCrawler:
    def __init__(self, urls_file="tplink_urls.txt", output_base_dir="tplink"):
        self.urls_file = urls_file
        self.output_base_dir = output_base_dir
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('tplink_batch_crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def crawl_batch(self):
        """批量爬取"""
        if not os.path.exists(self.urls_file):
            self.logger.error(f"URL文件不存在: {self.urls_file}")
            return
        
        # 读取URL列表
        with open(self.urls_file, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        
        if not urls:
            self.logger.error("没有找到有效的URL")
            return
        
        self.logger.info(f"开始批量爬取 {len(urls)} 个TP-Link产品")
        
        success_count = 0
        failed_urls = []
        
        for i, url in enumerate(urls, 1):
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"处理第 {i}/{len(urls)} 个产品: {url}")
            self.logger.info(f"{'='*60}")
            
            try:
                crawler = TPLinkCrawler(url, self.output_base_dir)
                result = crawler.crawl()
                
                if result:
                    success_count += 1
                    self.logger.info(f"✓ 第 {i} 个产品爬取成功")
                else:
                    failed_urls.append(url)
                    self.logger.error(f"✗ 第 {i} 个产品爬取失败")
                
            except Exception as e:
                failed_urls.append(url)
                self.logger.error(f"✗ 第 {i} 个产品爬取异常: {e}")
            
            # 添加延迟避免请求过快
            if i < len(urls):
                time.sleep(2)
        
        # 生成批量报告
        self._generate_batch_report(urls, success_count, failed_urls)
        
        self.logger.info(f"\n批量爬取完成!")
        self.logger.info(f"成功: {success_count}/{len(urls)}")
        self.logger.info(f"失败: {len(failed_urls)}/{len(urls)}")
    
    def _generate_batch_report(self, all_urls, success_count, failed_urls):
        """生成批量爬取报告"""
        try:
            report_file = os.path.join(self.output_base_dir, 'batch_report.txt')
            
            # 确保输出目录存在
            os.makedirs(self.output_base_dir, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("TP-Link批量爬取报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总URL数量: {len(all_urls)}\n")
                f.write(f"成功数量: {success_count}\n")
                f.write(f"失败数量: {len(failed_urls)}\n")
                f.write(f"成功率: {success_count/len(all_urls)*100:.1f}%\n")
                f.write("=" * 50 + "\n\n")
                
                if failed_urls:
                    f.write("失败的URL列表:\n")
                    f.write("-" * 30 + "\n")
                    for url in failed_urls:
                        f.write(f"{url}\n")
                    f.write("\n")
                
                f.write("所有URL列表:\n")
                f.write("-" * 30 + "\n")
                for i, url in enumerate(all_urls, 1):
                    status = "✓" if url not in failed_urls else "✗"
                    f.write(f"{i:2d}. {status} {url}\n")
            
            self.logger.info(f"批量报告保存: {report_file}")
            
        except Exception as e:
            self.logger.error(f"生成批量报告失败: {e}")

def main():
    """主函数"""
    import sys
    
    urls_file = "tplink_urls.txt"
    if len(sys.argv) > 1:
        urls_file = sys.argv[1]
    
    # 检查URL文件是否存在
    if not os.path.exists(urls_file):
        print(f"创建示例URL文件: {urls_file}")
        with open(urls_file, 'w', encoding='utf-8') as f:
            f.write("# TP-Link产品URL列表\n")
            f.write("# 每行一个URL，以#开头的行为注释\n")
            f.write("https://www.tp-linkshop.com.cn/Products/Details/1981\n")
            f.write("# 添加更多URL...\n")
        
        print(f"请编辑 {urls_file} 文件，添加要爬取的TP-Link产品URL")
        return
    
    # 开始批量爬取
    batch_crawler = TPLinkBatchCrawler(urls_file)
    batch_crawler.crawl_batch()

if __name__ == "__main__":
    main()
