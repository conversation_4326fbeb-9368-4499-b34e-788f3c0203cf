# TP-Link产品爬虫

基于锐捷爬虫架构开发的TP-Link产品信息爬虫，支持自动提取产品信息、技术参数、图片等数据。

## 🎯 功能特性

### ✅ 核心功能
- **产品基本信息提取**：自动识别产品名称、价格、描述
- **技术参数详细提取**：智能解析TP-Link特有的参数格式
- **产品图片下载**：批量下载产品图片并自动拼接
- **页面截图**：生成完整页面截图
- **智能去重**：自动去除重复参数，保留最佳格式

### 🔧 技术特点
- **基于Selenium**：支持动态内容加载和交互操作
- **智能参数解析**：专门针对TP-Link连续参数文本格式优化
- **批量处理**：支持从文件读取多个URL进行批量爬取
- **统一接口**：与锐捷爬虫共享统一的调用接口

## 📁 文件结构

```
ruijie_crawler/
├── tplink_crawler.py          # TP-Link爬虫核心类
├── tplink_main.py             # 单个产品爬取入口
├── tplink_batch_crawler.py    # 批量爬取脚本
├── tplink_urls.txt            # URL列表文件
├── unified_crawler.py         # 统一爬虫入口（支持锐捷+TP-Link）
├── tplink_analyzer.py         # 网站结构分析工具
└── tplink/                    # 输出目录
    └── {产品名称}/
        ├── images/            # 产品图片
        ├── texts/             # 文本信息
        │   ├── product_info.txt
        │   └── tech_specifications.txt
        ├── stitched_images.png
        └── page_screenshot.png
```

## 🚀 使用方法

### 1. 单个产品爬取

```bash
# 爬取单个TP-Link产品
python tplink_main.py https://www.tp-linkshop.com.cn/Products/Details/1981

# 使用统一爬虫（自动识别网站类型）
python unified_crawler.py https://www.tp-linkshop.com.cn/Products/Details/1981
```

### 2. 批量爬取

```bash
# 编辑URL列表文件
echo "https://www.tp-linkshop.com.cn/Products/Details/1981" >> tplink_urls.txt

# 执行批量爬取
python tplink_batch_crawler.py
```

### 3. 网站结构分析

```bash
# 分析TP-Link页面结构（开发调试用）
python tplink_analyzer.py
```

## 📊 提取效果

### 成功案例：TL-AP450I-PoE 薄款米兰金（方）

**基本信息：**
- 产品名称：TL-AP450I-PoE 薄款米兰金（方） 450M无线面板式AP 米兰金
- 产品价格：¥149.00

**技术参数（40项）：**
- 产品型号：TL-AP450I-PoE 薄款米兰金
- 安装方式：直接安装至国标86型网络接线暗盒中
- 尺寸：86*87.5*33mm
- 无线工作频段：2.4GHz-2.483GHz (中国)
- 无线速率：450Mbps
- 整机最大功耗：3.91W
- 工作温度：0℃～40℃
- SSID数量：8（支持中文SSID）
- 无线加密：WPA、WPA2、WPA-PSK、WPA2-PSK
- 最大可关联客户端数量：50
- ...等40个参数

**图片资源：**
- 下载35张产品图片
- 生成12.6MB拼接图片
- 包含产品展示图、详细图、规格图等

## 🔍 技术实现

### 页面结构分析
通过深入分析TP-Link网站发现：
- 产品名称位于`h4`标签
- 价格信息在`p.red`类中
- 技术参数通过点击"规格参数"标签动态加载
- 参数格式为连续文本，需要特殊解析

### 参数提取算法
1. **点击交互**：自动点击"规格参数"标签
2. **连续文本解析**：使用正则表达式解析TP-Link特有的连续参数格式
3. **智能分割**：根据参数名模式自动分割参数对
4. **去重优化**：基于参数名进行智能去重

### 关键技术点
```python
# TP-Link特有的连续参数解析
def _parse_continuous_params(self, text, specifications):
    param_patterns = [
        r'产品型号([^安装方式]+)',
        r'安装方式([^尺寸]+)',
        r'尺寸([^无线工作频段]+)',
        # ... 更多模式
    ]
```

## 🆚 与锐捷爬虫对比

| 特性 | 锐捷爬虫 | TP-Link爬虫 |
|------|----------|-------------|
| 参数格式 | 标准表格 | 连续文本 |
| 交互方式 | 点击"查看全部参数" | 点击"规格参数" |
| 参数数量 | 140-217项 | 40项 |
| 图片来源 | 多域名 | 主要来自tp-link.com.cn |
| 解析难度 | 中等 | 较高（需要特殊解析） |

## 📈 性能指标

- **成功率**：100%（测试产品）
- **参数提取**：40个技术参数
- **图片下载**：35张图片，12.6MB
- **处理时间**：约60秒/产品
- **内存占用**：适中

## 🔧 依赖要求

```bash
pip install selenium beautifulsoup4 pillow requests webdriver-manager
```

## 🎯 适用场景

1. **竞品分析**：快速获取TP-Link产品技术规格
2. **价格监控**：批量监控产品价格变化
3. **产品对比**：收集多个产品的详细参数
4. **市场研究**：分析TP-Link产品线和技术趋势

## 🚨 注意事项

1. **URL格式**：仅支持 `https://www.tp-linkshop.com.cn/Products/Details/` 格式
2. **访问频率**：建议添加适当延迟，避免请求过快
3. **网站变化**：如网站结构变化，可能需要调整解析逻辑
4. **合规使用**：请遵守网站robots.txt和使用条款

## 🔄 扩展性

该爬虫架构具有良好的扩展性：
- 可以轻松添加新的参数解析模式
- 支持新的TP-Link产品页面格式
- 可以集成到更大的爬虫框架中
- 支持与其他品牌爬虫统一管理

## 📝 更新日志

- **v1.0.0** (2025-07-24)
  - 初始版本发布
  - 支持基本产品信息提取
  - 实现技术参数智能解析
  - 集成图片下载和拼接功能
  - 支持批量处理和统一接口
