#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细调试脚本 - 模拟完整的爬取过程
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

def debug_extraction(url):
    """详细调试技术参数提取过程"""
    
    # 初始化WebDriver
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        print(f"正在访问页面: {url}")
        driver.get(url)
        time.sleep(5)
        
        # 1. 滚动到页面底部
        print("\n=== 步骤1: 滚动到页面底部 ===")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        # 2. 查找并点击"查看全部参数"
        print("\n=== 步骤2: 查找并点击查看全部参数 ===")
        selectors = [
            "//a[contains(text(), '查看全部参数')]",
            "//a[contains(text(), '全部参数')]",
            "//button[contains(text(), '查看全部参数')]",
            "//div[contains(text(), '查看全部参数')]"
        ]
        
        clicked = False
        for selector in selectors:
            try:
                wait = WebDriverWait(driver, 5)
                element = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                print(f"找到元素: {selector}")
                print(f"元素文本: {element.text}")
                
                driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(2)
                driver.execute_script("arguments[0].click();", element)
                print(f"成功点击: {selector}")
                time.sleep(8)  # 增加等待时间
                clicked = True
                break
            except Exception as e:
                print(f"尝试 {selector} 失败: {e}")
                continue
        
        if not clicked:
            print("未找到查看全部参数按钮")
        
        # 3. 再次滚动确保内容加载
        print("\n=== 步骤3: 再次滚动确保内容加载 ===")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(5)
        
        # 4. 获取页面内容并分析
        print("\n=== 步骤4: 分析页面内容 ===")
        content = driver.page_source
        soup = BeautifulSoup(content, 'lxml')
        
        # 查找所有包含技术关键词的文本
        tech_keywords = [
            '频率', '速率', '天线', '功率', 'MHz', 'GHz', 'Mbps', 'Gbps', 'dBi', 'W', 'IEEE', 
            '工作温度', '协议', '端口', '尺寸', '重量', '支持', '标准', 'Wi-Fi', 'WAN', 'LAN',
            '接口', '带宽', '传输', '覆盖', '范围', '电源', '功耗', '认证', '加密', '安全'
        ]
        
        page_text = soup.get_text()
        lines = page_text.split('\n')
        
        specifications = []
        for line in lines:
            line = line.strip()
            if (any(keyword in line for keyword in tech_keywords) and
                len(line) > 5 and len(line) < 300 and
                ('：' in line or ':' in line)):
                
                # 标准化分隔符
                if '：' in line:
                    parts = line.split('：', 1)
                else:
                    parts = line.split(':', 1)
                
                if len(parts) == 2:
                    param_name = parts[0].strip()
                    param_value = parts[1].strip()
                    if param_name and param_value and len(param_name) < 80:
                        specifications.append(f"{param_name}：{param_value}")
        
        # 去重
        unique_specs = []
        seen = set()
        for spec in specifications:
            if spec not in seen:
                unique_specs.append(spec)
                seen.add(spec)
        
        print(f"找到 {len(unique_specs)} 个技术参数:")
        for i, spec in enumerate(unique_specs, 1):
            print(f"{i:2d}. {spec}")
        
        # 5. 查找表格数据
        print("\n=== 步骤5: 查找表格数据 ===")
        tables = soup.find_all('table')
        print(f"找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            print(f"表格 {i+1}: {len(rows)} 行")
            for j, row in enumerate(rows[:5]):  # 只显示前5行
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    print(f"  行 {j+1}: {' | '.join(cell_texts)}")
        
        # 6. 查找特殊的参数容器
        print("\n=== 步骤6: 查找参数容器 ===")
        param_containers = soup.find_all(['div', 'section'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['param', 'spec', 'tech', 'detail', 'info']
        ))
        
        print(f"找到 {len(param_containers)} 个参数容器")
        for i, container in enumerate(param_containers):
            container_text = container.get_text(strip=True)
            if container_text and len(container_text) > 20:
                print(f"容器 {i+1}: {container_text[:100]}...")
        
        print("\n=== 调试完成 ===")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    url = "https://www.ruijiery.com/product/1925441870023266305.html"
    debug_extraction(url)
