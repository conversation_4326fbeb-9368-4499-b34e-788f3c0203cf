#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TP-Link爬虫主程序
"""

import sys
import os
from tplink_crawler import TPLinkCrawler

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python tplink_main.py <TP-Link产品URL>")
        print("示例: python tplink_main.py https://www.tp-linkshop.com.cn/Products/Details/1981")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # 验证URL
    if not url.startswith('https://www.tp-linkshop.com.cn/Products/Details/'):
        print("错误: 请提供有效的TP-Link产品URL")
        print("URL应该以 https://www.tp-linkshop.com.cn/Products/Details/ 开头")
        sys.exit(1)
    
    print(f"开始爬取TP-Link产品: {url}")
    
    # 创建爬虫实例并执行爬取
    crawler = TPLinkCrawler(url)
    result = crawler.crawl()
    
    if result:
        print(f"✓ 爬取完成！")
        print(f"输出目录: {result}")
    else:
        print("✗ 爬取失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
