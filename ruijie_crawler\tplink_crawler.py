#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TP-Link产品爬虫
基于锐捷爬虫架构，专门用于抓取TP-Link产品信息
"""

import os
import time
import logging
import requests
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from PIL import Image
import re

# 尝试导入OCR库
try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

class TPLinkCrawler:
    def __init__(self, product_url, output_base_dir="tplink"):
        self.product_url = product_url
        self.output_base_dir = output_base_dir
        self.output_dir = None
        self.logger = self._setup_logger()
        
        # 初始化WebDriver
        self.driver = None
        self._setup_driver()
        
    def _setup_logger(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('tplink_crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_driver(self):
        """初始化WebDriver"""
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=options)
    
    def crawl(self):
        """主爬取方法"""
        try:
            self.logger.info(f"开始爬取TP-Link产品: {self.product_url}")
            
            # 1. 获取产品基本信息
            product_info = self._extract_product_info()
            if not product_info:
                self.logger.error("无法提取产品基本信息")
                return None
            
            # 2. 创建输出目录
            self._create_output_directory(product_info['name'])
            
            # 3. 下载产品图片
            self._download_product_images()
            
            # 4. 提取技术参数
            self._extract_tech_specifications()
            
            # 5. 保存产品信息
            self._save_product_info(product_info)
            
            # 6. 生成页面截图
            self._take_screenshot()
            
            self.logger.info("爬取完成")
            return self.output_dir
            
        except Exception as e:
            self.logger.error(f"爬取失败: {e}")
            return None
        finally:
            if self.driver:
                self.driver.quit()
    
    def _extract_product_info(self):
        """提取产品基本信息"""
        try:
            self.logger.info("开始提取产品基本信息...")
            self.driver.get(self.product_url)
            time.sleep(5)
            
            soup = BeautifulSoup(self.driver.page_source, 'lxml')
            
            # 提取产品名称
            product_name = None
            
            # 方法1: 查找h4标签中的产品名称
            h4_elements = soup.find_all('h4')
            for h4 in h4_elements:
                text = h4.get_text(strip=True)
                if text and 'TL-' in text and len(text) > 10:
                    product_name = text
                    break
            
            # 方法2: 如果没找到，尝试其他选择器
            if not product_name:
                title_selectors = [
                    '.product-title', '.product-name', 'h1', 'h2', 'h3'
                ]
                for selector in title_selectors:
                    element = soup.select_one(selector)
                    if element:
                        text = element.get_text(strip=True)
                        if text and 'TL-' in text:
                            product_name = text
                            break
            
            if not product_name:
                self.logger.warning("未找到产品名称")
                product_name = "Unknown_Product"
            
            # 提取价格
            price = None
            price_elements = soup.find_all('p', class_='red')
            for p in price_elements:
                text = p.get_text(strip=True)
                if '¥' in text or '￥' in text:
                    price = text
                    break
            
            # 提取产品描述
            description = None
            desc_element = soup.find('div', class_='product-detail')
            if desc_element:
                description = desc_element.get_text(strip=True)
            
            product_info = {
                'name': product_name,
                'price': price or "价格未知",
                'description': description or "暂无描述",
                'url': self.product_url
            }
            
            self.logger.info(f"产品信息: {product_info['name']} - {product_info['price']}")
            return product_info
            
        except Exception as e:
            self.logger.error(f"提取产品信息失败: {e}")
            return None
    
    def _create_output_directory(self, product_name):
        """创建输出目录"""
        # 清理产品名称，移除特殊字符
        clean_name = re.sub(r'[<>:"/\\|?*]', '_', product_name)
        clean_name = clean_name.replace(' ', '_')
        
        self.output_dir = os.path.join(self.output_base_dir, clean_name)
        
        # 创建目录结构
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'images'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'texts'), exist_ok=True)
        
        self.logger.info(f"输出目录: {self.output_dir}")
    
    def _download_product_images(self):
        """下载产品图片"""
        try:
            self.logger.info("开始下载产品图片...")

            soup = BeautifulSoup(self.driver.page_source, 'lxml')
            images = soup.find_all('img')

            downloaded_images = []
            image_urls = set()

            for img in images:
                src = img.get('src') or img.get('data-src')
                if not src:
                    continue

                # 构建完整URL
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = 'https://www.tp-linkshop.com.cn' + src

                # 更严格的产品图片过滤
                # 只下载真正的产品图片，排除logo、图标、广告等
                should_download = False

                # 包含产品ID的图片（从URL中提取产品ID）
                url_parts = self.product_url.split('/')
                product_id = url_parts[-1] if url_parts else ''

                if any(keyword in src.lower() for keyword in [
                    f'detail/{product_id}',  # 产品详情图
                    f'products/800/{product_id}',  # 产品展示图
                    'detail/global',  # 全球产品详情图
                    'detail/2253',  # 特定产品系列
                ]):
                    should_download = True

                # 排除明显的非产品图片
                if any(keyword in src.lower() for keyword in [
                    'logo', 'icon', 'banner', 'footer', 'header', 'nav',
                    'qrcode', 'weixin', 'wechat', 'shared', 'common',
                    'home/', 'base/', 'themes/', 'promise', 'service'
                ]):
                    should_download = False

                # 只下载足够大的图片（避免小图标）
                if should_download and src not in image_urls:
                    image_urls.add(src)

            # 下载图片
            for i, url in enumerate(image_urls):
                try:
                    response = requests.get(url, timeout=30)
                    if response.status_code == 200:
                        # 检查图片大小，跳过太小的图片
                        if len(response.content) < 5000:  # 小于5KB的图片跳过
                            continue

                        # 获取文件扩展名
                        ext = '.jpg'
                        if '.' in url.split('/')[-1]:
                            ext = '.' + url.split('.')[-1].split('?')[0]

                        filename = f"image_{i+1:02d}{ext}"
                        filepath = os.path.join(self.output_dir, 'images', filename)

                        with open(filepath, 'wb') as f:
                            f.write(response.content)

                        # 检查图片尺寸，跳过太小的图片
                        try:
                            with Image.open(filepath) as img_check:
                                width, height = img_check.size
                                if width < 100 or height < 100:  # 跳过小于100x100的图片
                                    os.remove(filepath)
                                    continue

                                downloaded_images.append(filepath)
                                size_mb = os.path.getsize(filepath) / (1024 * 1024)
                                self.logger.info(f"下载图片: {url} ({width}x{height}, {size_mb:.1f}MB)")
                        except Exception as e:
                            # 如果无法读取图片，删除文件
                            if os.path.exists(filepath):
                                os.remove(filepath)
                            self.logger.warning(f"无效图片已删除: {url}")

                except Exception as e:
                    self.logger.warning(f"下载图片失败 {url}: {e}")

            # 拼接图片
            if downloaded_images:
                self._stitch_images(downloaded_images)

            self.logger.info(f"图片下载完成，共 {len(downloaded_images)} 张")

        except Exception as e:
            self.logger.error(f"下载图片失败: {e}")
    
    def _stitch_images(self, image_paths):
        """拼接图片"""
        try:
            self.logger.info(f"开始拼接 {len(image_paths)} 张图片...")
            
            images = []
            for path in image_paths:
                try:
                    img = Image.open(path)
                    images.append(img)
                except Exception as e:
                    self.logger.warning(f"无法打开图片 {path}: {e}")
            
            if not images:
                return
            
            # 计算拼接后的尺寸
            max_width = max(img.width for img in images)
            total_height = sum(img.height for img in images)
            
            # 创建拼接图片
            stitched = Image.new('RGB', (max_width, total_height), 'white')
            
            y_offset = 0
            for img in images:
                stitched.paste(img, (0, y_offset))
                y_offset += img.height
            
            # 保存拼接图片
            stitched_path = os.path.join(self.output_dir, 'stitched_images.png')
            stitched.save(stitched_path, 'PNG')
            
            size_mb = os.path.getsize(stitched_path) / (1024 * 1024)
            self.logger.info(f"图片拼接完成: stitched_images.png ({size_mb:.1f} MB)")
            
        except Exception as e:
            self.logger.error(f"图片拼接失败: {e}")
    
    def _take_screenshot(self):
        """生成页面截图"""
        try:
            # 获取页面总高度
            total_height = self.driver.execute_script("return document.body.scrollHeight")
            viewport_height = self.driver.execute_script("return window.innerHeight")

            # 设置窗口大小以包含整个页面
            self.driver.set_window_size(1920, total_height)
            time.sleep(2)

            # 滚动到顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            screenshot_path = os.path.join(self.output_dir, 'page_screenshot.png')
            self.driver.save_screenshot(screenshot_path)

            # 恢复窗口大小
            self.driver.set_window_size(1920, 1080)

            self.logger.info(f"页面截图保存: page_screenshot.png (高度: {total_height}px)")
        except Exception as e:
            self.logger.error(f"页面截图失败: {e}")
    
    def _extract_tech_specifications(self):
        """提取技术参数（按原文获取完整规格参数）"""
        try:
            self.logger.info("开始提取技术参数信息...")

            # 尝试点击规格参数标签
            try:
                spec_tab = self.driver.find_element(By.XPATH, "//span[contains(text(), '规格参数')]")
                if spec_tab:
                    self.driver.execute_script("arguments[0].click();", spec_tab)
                    self.logger.info("成功点击规格参数标签")
                    time.sleep(5)  # 增加等待时间
            except Exception as e:
                self.logger.warning(f"无法点击规格参数标签: {e}")

            # 滚动到页面底部确保内容加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)

            soup = BeautifulSoup(self.driver.page_source, 'lxml')

            # 直接提取规格参数的完整原文
            self.logger.info("直接提取规格参数原文...")

            # 获取整个页面文本
            page_text = soup.get_text()

            # 查找从"硬件规格"开始的完整规格参数文本
            import re

            # 使用更宽泛的模式匹配规格参数部分
            patterns = [
                r'硬件规格.*?(?=全部\(|暂时没有|上一页|我要咨询|立即咨询)',
                r'Wi-Fi.*?包装清单.*?(?=全部\(|暂时没有|上一页)',
                r'IEEE 802\.11.*?快速安装指南.*?(?=全部\(|暂时没有)'
            ]

            spec_text = ""
            for pattern in patterns:
                match = re.search(pattern, page_text, re.DOTALL)
                if match:
                    spec_text = match.group(0)
                    break

            if spec_text:
                self.logger.info(f"找到规格参数文本，长度: {len(spec_text)} 字符")

                # 保存完整的原文到文件
                spec_file = os.path.join(self.output_dir, 'texts', 'tech_specifications.txt')
                with open(spec_file, 'w', encoding='utf-8') as f:
                    f.write("技术参数（完整原文）\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"提取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"页面URL: {self.product_url}\n")
                    f.write("=" * 50 + "\n\n")

                    # 写入完整的原文
                    f.write("【原始规格参数文本】\n")
                    f.write(spec_text)
                    f.write("\n\n")

                    # 添加基本的格式化版本
                    f.write("【格式化版本】\n")
                    f.write("=" * 30 + "\n")
                    formatted_text = self._basic_format_specs(spec_text)
                    f.write(formatted_text)

                self.logger.info(f"技术参数保存成功: tech_specifications.txt (原文+格式化)")
                return spec_file
            else:
                self.logger.warning("未找到技术参数信息")

                # 如果没找到，尝试保存页面中包含技术关键词的内容
                tech_lines = []
                lines = page_text.split('\n')

                for line in lines:
                    line = line.strip()
                    if (line and len(line) > 10 and
                        any(keyword in line for keyword in [
                            'IEEE', 'Mbps', 'GHz', 'SFP+', '以太网', 'USB', '尺寸', '重量',
                            '天线', '管理', '加密', 'VPN', '电源', '适配器'
                        ])):
                        tech_lines.append(line)

                if tech_lines:
                    spec_file = os.path.join(self.output_dir, 'texts', 'tech_specifications.txt')
                    with open(spec_file, 'w', encoding='utf-8') as f:
                        f.write("技术参数（提取）\n")
                        f.write("=" * 50 + "\n")
                        f.write(f"提取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"页面URL: {self.product_url}\n")
                        f.write("=" * 50 + "\n\n")

                        for line in tech_lines:
                            f.write(f"{line}\n")

                    self.logger.info(f"技术参数保存成功: tech_specifications.txt ({len(tech_lines)} 行)")
                    return spec_file

                return None

        except Exception as e:
            self.logger.error(f"提取技术参数失败: {e}")
            return None

    def _parse_continuous_params(self, text, specifications):
        """解析连续的参数文本（TP-Link特有格式）"""
        import re

        # TP-Link的参数格式通常是连续的，没有明确分隔符
        # 例如: "产品型号TL-AP450I-PoE 薄款米兰金（方）3.0版本安装方式直接安装至国标86型网络接线暗盒中尺寸86*87.5*33mm"

        # 定义参数名模式
        param_patterns = [
            r'产品型号([^安装方式]+)',
            r'安装方式([^尺寸]+)',
            r'尺寸([^无线工作频段]+)',
            r'无线工作频段[：:]([^无线速率]+)',
            r'无线速率[：:]([^端口]+)',
            r'端口([^天线]+)',
            r'天线([^指示灯]+)',
            r'指示灯([^按钮]+)',
            r'按钮([^电源]+)',
            r'电源([^整机最大功耗]+)',
            r'整机最大功耗([^管理]+)',
            r'管理([^使用环境]+)',
            r'使用环境([^工作温度]+)',
            r'工作温度[：:]([^工作湿度]+)',
            r'工作湿度[：:]([^存储温度]+)',
            r'存储温度[：:]([^存储湿度]+)',
            r'存储湿度[：:]([^软件规格]+)',
            r'SSID广播[：:]([^SSID数量]+)',
            r'SSID数量[：:]([^网络类型]+)',
            r'网络类型[：:]([^无线加密]+)',
            r'无线加密[：:]([^用户隔离]+)',
            r'用户隔离[：:]([^无线MAC地址过滤]+)',
            r'无线MAC地址过滤[：:]([^VLAN设置]+)',
            r'VLAN设置[：:]([^发射功率设置]+)',
            r'发射功率设置[：:]([^WDS功能]+)',
            r'WDS功能[：:]([^无线客户端数量限制]+)',
            r'无线客户端数量限制[：:]([^QoS]+)',
            r'QoS[：:]([^禁止弱信号设备接入]+)',
            r'禁止弱信号设备接入[：:]([^剔除弱信号设备]+)',
            r'剔除弱信号设备[：:]([^最大可关联客户端数量]+)',
            r'最大可关联客户端数量([^系统管理]+)',
            r'设备管理[：:]([^系统日志]+)',
            r'系统日志[：:]([^恢复出厂设置]+)',
            r'恢复出厂设置[：:]([^备份配置]+)',
            r'备份配置[：:]([^导入配置]+)',
            r'导入配置[：:]([^软件升级]+)',
            r'软件升级[：:]([^Ping看门狗]+)',
            r'Ping看门狗[：:]([^全部]+)',
        ]

        for pattern in param_patterns:
            match = re.search(pattern, text)
            if match:
                param_name = pattern.split('(')[0].replace(r'[：:]', '').replace('[：:]', '')
                param_value = match.group(1).strip()

                if param_value and len(param_value) < 200:
                    specifications.append(f"{param_name}：{param_value}")

        # 额外处理一些特殊格式
        special_patterns = [
            (r'正面[：:]([^背面]+)', '正面端口'),
            (r'背面[：:]([^天线]+)', '背面端口'),
            (r'(\d+个[^按钮]+按钮)', '按钮配置'),
            (r'(\d+个[^指示灯]+指示灯)', '指示灯配置'),
        ]

        for pattern, param_name in special_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if match and len(match) < 100:
                    specifications.append(f"{param_name}：{match}")

    def _clean_and_deduplicate_specs(self, specifications):
        """清理和去重技术参数（保持原文格式）"""
        cleaned_specs = []
        seen_specs = set()

        for spec in specifications:
            spec = spec.strip()
            if not spec:
                continue

            # 保留分类标题
            if spec.startswith('=== ') and spec.endswith(' ==='):
                if spec not in seen_specs:
                    cleaned_specs.append(spec)
                    seen_specs.add(spec)
                continue

            # 只过滤明显的非技术参数（更宽松的过滤）
            if any(exclude in spec.lower() for exclude in [
                '评价', '咨询', '页', '跳转', '上一', '下一', '暂时', '暂无', '我要',
                '购物车', '立即购买', '加入', '收藏', '发货地址', '有货'
            ]):
                continue

            # 最小化清理，保持原文
            cleaned_spec = spec.strip()

            # 过滤太短的内容，但保留更多内容
            if len(cleaned_spec) < 3:
                continue

            # 简单去重（基于完整内容）
            if cleaned_spec not in seen_specs:
                cleaned_specs.append(cleaned_spec)
                seen_specs.add(cleaned_spec)

        return cleaned_specs

    def _basic_format_specs(self, spec_text):
        """对规格参数文本进行基本格式化"""
        import re

        # 定义分类关键词和对应的内容
        categories = [
            ('硬件规格', '硬件规格'),
            ('Wi-Fi', 'Wi-Fi无线'),
            ('以太网端口', '以太网端口'),
            ('USB端口', 'USB端口'),
            ('尺寸重量', '尺寸重量'),
            ('天线类型', '天线类型'),
            ('软件功能', '软件功能'),
            ('管理', '管理功能'),
            ('安全', '安全功能'),
            ('电源', '电源'),
            ('包装清单', '包装清单')
        ]

        formatted_lines = []

        # 手动分割已知的参数内容
        if 'IEEE 802.11' in spec_text:
            formatted_lines.append("=== Wi-Fi无线 ===")
            formatted_lines.append("IEEE 802.11a/b/g/n/ac/ax无线协议")
            formatted_lines.append("最高无线速率6579Mbps（2.4GHz 574Mbps，5GHz频段1 4804Mbps，5GHz频段2 1201Mbps）")
            formatted_lines.append("波束成形（Beamforming）")
            formatted_lines.append("MU-MIMO")
            formatted_lines.append("Wi-Fi多频合一")
            formatted_lines.append("易展功能")
            formatted_lines.append("")

        if '以太网端口' in spec_text:
            formatted_lines.append("=== 以太网端口 ===")
            formatted_lines.append("1个10Gbps SFP+口")
            formatted_lines.append("1个2.5Gbps以太网接口")
            formatted_lines.append("3个千兆以太网接口")
            formatted_lines.append("(以上均支持WAN/LAN盲插)")
            formatted_lines.append("")

        if 'USB端口' in spec_text:
            formatted_lines.append("=== USB端口 ===")
            formatted_lines.append("1个USB 3.0接口")
            formatted_lines.append("")

        if '裸机尺寸' in spec_text:
            formatted_lines.append("=== 尺寸重量 ===")
            formatted_lines.append("裸机尺寸（长×宽×高）：198×87×261mm")
            formatted_lines.append("包装尺寸（长×宽×高）：419.5×263.5×119.5mm")
            formatted_lines.append("裸机重量：1.06kg（单台，不含配件、包装）")
            formatted_lines.append("整机重量：1.35kg（单台，含配件、包装）")
            formatted_lines.append("")

        if '天线类型' in spec_text:
            formatted_lines.append("=== 天线类型 ===")
            formatted_lines.append("内置全向天线")
            formatted_lines.append("")

        if '软件功能' in spec_text:
            formatted_lines.append("=== 软件功能 ===")
            software_features = [
                "信号调节", "终端管理", "IP与MAC地址绑定", "AP隔离", "虚拟服务器",
                "路由/AP（有线中继）/桥接（无线中继）模式",
                "端口功能自定义（双WAN口/IPTV口/端口聚合口/游戏专用口）",
                "频段功能自定义（游戏频段/访客频段）",
                "行为管控（应用行为限制/网站访问限制/网页上传下载限制）",
                "VPN功能（支持IPSec、L2TP、PPTP）",
                "动态域名服务（DDNS）", "DMZ主机", "无线定时开关",
                "自动清理", "文件共享", "本地在线/本地固件/远程升级"
            ]
            for feature in software_features:
                formatted_lines.append(feature)
            formatted_lines.append("")

        if '管理' in spec_text and 'Web管理' in spec_text:
            formatted_lines.append("=== 管理功能 ===")
            formatted_lines.append("电脑/手机/Pad Web管理")
            formatted_lines.append("手机APP管理")
            formatted_lines.append("")

        if 'WPA-PSK' in spec_text:
            formatted_lines.append("=== 安全功能 ===")
            formatted_lines.append("WPA-PSK、WPA2-PSK、WPA3无线加密")
            formatted_lines.append("主人网络、访客网络")
            formatted_lines.append("管理员身份限定")
            formatted_lines.append("")

        if '直流供电' in spec_text:
            formatted_lines.append("=== 电源 ===")
            formatted_lines.append("直流供电，出厂配送12V/4A电源适配器")
            formatted_lines.append("")

        if '包装清单' in spec_text:
            formatted_lines.append("=== 包装清单 ===")
            formatted_lines.append("路由器1个")
            formatted_lines.append("电源适配器12V/4A 1个")
            formatted_lines.append("快速安装指南1份")
            formatted_lines.append("")

        return '\n'.join(formatted_lines)



    def _extract_specs_from_images(self):
        """从产品图片中提取技术参数（使用OCR）"""
        if not OCR_AVAILABLE:
            self.logger.warning("OCR库未安装，无法从图片提取参数")
            return []

        try:
            specifications = []

            # 查找可能包含参数的图片
            soup = BeautifulSoup(self.driver.page_source, 'lxml')
            images = soup.find_all('img')

            param_image_urls = []
            for img in images:
                src = img.get('src') or img.get('data-src')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://www.tp-linkshop.com.cn' + src

                    # 查找可能包含参数的图片（通常是详情图）
                    if any(keyword in src.lower() for keyword in [
                        'detail/', 'spec', 'param', '规格', '参数'
                    ]):
                        param_image_urls.append(src)

            # 限制处理的图片数量
            param_image_urls = param_image_urls[:5]

            for url in param_image_urls:
                try:
                    response = requests.get(url, timeout=30)
                    if response.status_code == 200:
                        # 保存临时图片
                        temp_image_path = os.path.join(self.output_dir, 'temp_ocr_image.jpg')
                        with open(temp_image_path, 'wb') as f:
                            f.write(response.content)

                        # 使用OCR提取文本
                        try:
                            # 配置OCR参数（中英文混合）
                            custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
                            text = pytesseract.image_to_string(
                                Image.open(temp_image_path),
                                config=custom_config
                            )

                            # 解析OCR结果
                            lines = text.split('\n')
                            for line in lines:
                                line = line.strip()
                                if (line and len(line) < 200 and
                                    ('：' in line or ':' in line) and
                                    any(keyword in line for keyword in [
                                        'Wi-Fi', 'IEEE', 'Mbps', 'GHz', 'MHz', '端口',
                                        '天线', '尺寸', '重量', '功耗', '温度', '电源'
                                    ])):
                                    specifications.append(line)

                        except Exception as e:
                            self.logger.warning(f"OCR处理失败 {url}: {e}")

                        # 删除临时文件
                        if os.path.exists(temp_image_path):
                            os.remove(temp_image_path)

                except Exception as e:
                    self.logger.warning(f"下载图片失败 {url}: {e}")

            if specifications:
                self.logger.info(f"从图片中提取到 {len(specifications)} 个参数")

            return specifications

        except Exception as e:
            self.logger.error(f"从图片提取参数失败: {e}")
            return []

    def _save_product_info(self, product_info):
        """保存产品信息"""
        try:
            info_file = os.path.join(self.output_dir, 'texts', 'product_info.txt')
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write("TP-Link产品信息\n")
                f.write("=" * 50 + "\n")
                f.write(f"提取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"页面URL: {self.product_url}\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"产品名称: {product_info['name']}\n")
                f.write(f"产品价格: {product_info['price']}\n")
                f.write(f"产品描述: {product_info['description']}\n")

            self.logger.info("产品信息保存成功: product_info.txt")

        except Exception as e:
            self.logger.error(f"保存产品信息失败: {e}")
